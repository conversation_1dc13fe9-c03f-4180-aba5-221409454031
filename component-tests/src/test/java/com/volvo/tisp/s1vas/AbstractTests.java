/*
package com.volvo.tisp.s1vas;

import static com.wirelesscar.componentbase.test.rules.Rules.setupConnectivityFor;
import static com.volvo.tisp.s1vas.TestUtils.environment;

import org.junit.After;
import org.junit.Before;
import org.junit.ClassRule;
import org.junit.Rule;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.rules.SpringClassRule;
import org.springframework.test.context.junit4.rules.SpringMethodRule;

import com.wirelesscar.config.mock.MockConfiguration;

@ContextConfiguration(classes = ConfigForTests.class)
public abstract class AbstractTests {

  private static final MockConfiguration cfg = MockConfiguration.getConfig();

  @ClassRule
  public static final SpringClassRule SPRING_CLASS_RULE = new SpringClassRule();


  @Rule
  public final SpringMethodRule methodRule = setupConnectivityFor(environment())
      .withHttp()
      .withAuthorizedClient()
      .buildForSpring();

  @Before
  public void setup() throws Exception {

  }

  @After
  public void tearDown() throws Exception {

  }
}
*/
