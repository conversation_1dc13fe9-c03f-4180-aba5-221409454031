/*
package com.volvo.tisp.s1vas.staticapidocs;

import org.junit.runner.RunWith;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import com.wirelesscar.componentbase.test.apidocumentation.RemoteStaticAPIDocsGeneration;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = StaticAPIDocsGeneration.StaticAPIDocsGenerationConfig.class)
public class StaticAPIDocsGeneration extends RemoteStaticAPIDocsGeneration {

  @Configuration
  public static class StaticAPIDocsGenerationConfig {
    @Bean
    public GenerationConfigBuilder config() {
      return GenerationConfigBuilder.create().shortName("s1vas").longName("sems1-vehicle-api-server").port(44760);
    }
  }
}
*/
