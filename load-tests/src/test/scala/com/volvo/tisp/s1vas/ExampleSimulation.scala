package com.volvo.exampleloadtestTODOREPLACEMEbeforeusing

import io.gatling.core.Predef._
import io.gatling.http.Predef._

class ExampleSimulation extends Simulation {
  val targetHost = System.getProperty("SUT_HOST", "************")
  
  val scn = scenario("Demo scenario")
            .exec(http("Ping request")
              .get("http://" + targetHost + ":12345/ping/pong"))

  setUp(
    scn.inject(
      atOnceUsers(5)
    ).protocols(http)
  )
}
