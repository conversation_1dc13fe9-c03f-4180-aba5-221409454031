<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

  <modelVersion>4.0.0</modelVersion>

  <!-- +=============================================== -->
  <!-- | Section 1: Project information -->
  <!-- +=============================================== -->
  <parent>
    <groupId>com.volvo.tisp.s1vas</groupId>
    <artifactId>sems1-vehicle-api-server</artifactId>
    <version>0-SNAPSHOT</version>
    <relativePath>../</relativePath>
  </parent>

  <artifactId>sems1-vehicle-api-server-load-tests</artifactId>
  <packaging>jar</packaging>

  <name>Sems1 Vehicle Api :: Server :: Load Tests</name>
  <url/>

  <properties>
    <maven.test.skip>false</maven.test.skip>
  </properties>

  <!-- +=============================================== -->
  <!-- | Section 2: Dependency (Management) -->
  <!-- +=============================================== -->
  <dependencies>

    <!-- Our own Client -->
    <dependency>
       <groupId>com.volvo.tisp.s1vas</groupId>
       <artifactId>sems1-vehicle-api-client-impl-http</artifactId>
       <scope>test</scope>
    </dependency>

    <!-- Platform -->
    <dependency>
      <groupId>com.wirelesscar.config-lib</groupId>
      <artifactId>config-lib-impl-mock</artifactId>
    </dependency>

    <!-- Frameworks -->
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.glassfish.jersey.core</groupId>
      <artifactId>jersey-client</artifactId>
      <scope>test</scope>
    </dependency>

    <!-- Logging -->

    <!-- Utils -->
    <dependency>
      <groupId>com.github.tomakehurst</groupId>
      <artifactId>wiremock</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.glassfish.jersey.media</groupId>
      <artifactId>jersey-media-json-jackson</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.glassfish.jersey.media</groupId>
      <artifactId>jersey-media-jaxb</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>io.gatling.highcharts</groupId>
      <artifactId>gatling-charts-highcharts</artifactId>
      <version>${gatling.version}</version>
    </dependency>

  </dependencies>

  <!-- +=============================================== -->
  <!-- | Section 3: Profiles -->
  <!-- +=============================================== -->
    <profiles>
      <profile>
        <id>load-tests</id>
        <build>
          <plugins>
            <plugin>
              <groupId>org.apache.maven.plugins</groupId>
              <artifactId>maven-surefire-plugin</artifactId>
              <configuration>
                <skip>true</skip>
              </configuration>
            </plugin>
            <plugin>
              <groupId>io.gatling</groupId>
              <artifactId>gatling-maven-plugin</artifactId>
              <executions>
                <execution>
                  <phase>integration-test</phase>
                  <goals>
                    <goal>integration-test</goal>
                  </goals>
                  <configuration>
                    <simulationClass>com.volvo.exampleloadtestTODOREPLACEMEbeforeusing.ExampleSimulation</simulationClass>
                  </configuration>
                </execution>
              </executions>
            </plugin>
          </plugins>
        </build>
      </profile>
    </profiles>
</project>
