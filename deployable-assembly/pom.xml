<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

   <modelVersion>4.0.0</modelVersion>

   <!-- +=============================================== -->
   <!-- | Section 1: Project information -->
   <!-- +=============================================== -->
   <parent>
      <groupId>com.volvo.tisp.s1vas</groupId>
      <artifactId>sems1-vehicle-api-server</artifactId>
      <version>0-SNAPSHOT</version>
   </parent>

   <groupId>com.wirelesscar.ngtp.cd.assemblies</groupId>
   <artifactId>sems1-vehicle-api-server-deployable</artifactId>
   <packaging>pom</packaging>

   <name>Sems1 Vehicle Api :: Server :: Deployable Assembly</name>

   <properties>
      <deploy.engine.jar.version>182</deploy.engine.jar.version>

      <deploy.engine.maven.plugin.version>41</deploy.engine.maven.plugin.version>
      <deploy.engine.version>26</deploy.engine.version>
      <supervisor3x.version>38</supervisor3x.version>
      <jdk17.version>7</jdk17.version>

   </properties>


   <!-- +=============================================== -->
   <!-- | Section 2: Dependency (management) settings -->
   <!-- +=============================================== -->
   <dependencies>
      <!-- Internal dependencies -->
      <dependency>
         <groupId>com.volvo.tisp.s1vas</groupId>
         <artifactId>sems1-vehicle-api-server-app</artifactId>
         <version>${project.version}</version>
         <type>jar</type>
      </dependency>

      <!-- Deployers -->
      <dependency>
         <groupId>com.wirelesscar.framework.deploy-engine</groupId>
         <artifactId>deploy-engine-jar-deployer</artifactId>
         <version>${deploy.engine.jar.version}</version>
         <classifier>bundle</classifier>
         <type>zip</type>
      </dependency>

      <!-- Deploy Engine -->
      <dependency>
         <groupId>com.wirelesscar.framework.deploy-engine</groupId>
         <artifactId>deploy-engine</artifactId>
         <version>${deploy.engine.version}</version>
         <type>pom</type>
      </dependency>

      <!-- External dependencies / Infrastructure -->
      <dependency>
         <groupId>net.java.jdk</groupId>
         <artifactId>jdk17</artifactId>
         <version>${jdk17.version}</version>
         <type>npm</type>
         <scope>provided</scope>
      </dependency>
      <dependency>
         <groupId>org.supervisord</groupId>
         <artifactId>supervisor3x</artifactId>
         <version>${supervisor3x.version}</version>
         <type>npm</type>
         <scope>provided</scope>
      </dependency>
   </dependencies>

   <!-- +=============================================== -->
   <!-- | Section 3: Build settings -->
   <!-- +=============================================== -->
   <build>
      <plugins>
         <plugin>
            <groupId>com.wirelesscar.framework.deploy-engine</groupId>
            <artifactId>deploy-engine-maven-plugin</artifactId>
            <version>${deploy.engine.maven.plugin.version}</version>
            <executions>
               <execution>
                  <goals>
                     <goal>package</goal>
                  </goals>
               </execution>
            </executions>
         </plugin>
      </plugins>
   </build>
</project>
