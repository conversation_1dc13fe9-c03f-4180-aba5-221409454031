<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

  <modelVersion>4.0.0</modelVersion>

  <!-- +=============================================== -->
  <!-- | Section 1: Project information -->
  <!-- +=============================================== -->
  <parent>
    <groupId>com.volvo.tisp.s1vas</groupId>
    <artifactId>sems1-vehicle-api-server</artifactId>
    <version>0-SNAPSHOT</version>
  </parent>

  <artifactId>sems1-vehicle-api-server-app</artifactId>
  <packaging>jar</packaging>
  <name>Sems1 Vehicle Api :: Server :: Application</name>
  <url/>

  <properties>
    <spring-boot.mainClass>com.volvo.tisp.s1vas.conf.Application</spring-boot.mainClass>

  </properties>

  <!-- +=============================================== -->
  <!-- | Section 2: Dependency (Management) -->
  <!-- +=============================================== -->
  <dependencies>

    <!-- Our API -->
    <dependency>
      <groupId>com.volvo.tisp.s1vac</groupId>
      <artifactId>sems1-vehicle-api-client-api</artifactId>
    </dependency>

    <!-- Configuration -->
    <dependency>
      <groupId>com.wirelesscar.config-lib</groupId>
      <artifactId>config-lib-impl-property</artifactId>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.wirelesscar.config-lib</groupId>
      <artifactId>config-lib-impl-zookeeper</artifactId>
      <scope>runtime</scope>
    </dependency>

    <!-- Frameworks -->
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-webmvc</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-web</artifactId>
    </dependency>
    <dependency>
      <groupId>com.sun.xml.messaging.saaj</groupId>
      <artifactId>saaj-impl</artifactId>
    </dependency>

    <!-- Metrics -->
    <dependency>
      <groupId>io.micrometer</groupId>
      <artifactId>micrometer-registry-influx</artifactId>
    </dependency>

    <!-- spring-boot -->
    <dependency>
      <groupId>com.volvo.tisp.framework</groupId>
      <artifactId>tisp-framework-starter-web</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-autoconfigure</artifactId>
      <scope>compile</scope>
    </dependency>

    <!-- TEST -->
    <dependency>
      <groupId>com.wirelesscar.config-lib</groupId>
      <artifactId>config-lib-impl-mock</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-simple</artifactId>
      <scope>test</scope>
    </dependency>

    <!-- Sems-Security -->
    <dependency>
      <groupId>com.volvo.tisp.semsc</groupId>
      <artifactId>sems-security-client-api</artifactId>
    </dependency>
    <dependency>
      <groupId>com.volvo.tisp.semsc</groupId>
      <artifactId>sems-security-client-impl-http</artifactId>
    </dependency>
    <!--Sems1 core component -->
    <dependency>
      <groupId>com.volvo.tisp.sem1core</groupId>
      <artifactId>sems1-core-client-api</artifactId>
    </dependency>
    <dependency>
      <groupId>com.volvo.tisp.sem1core</groupId>
      <artifactId>sems1-core-client-impl-http</artifactId>
    </dependency>
    <dependency>
      <groupId>com.wirelesscar.functionalchecks</groupId>
      <artifactId>functional-checks-api</artifactId>
    </dependency>

    <!-- VSCS -->
    <dependency>
      <groupId>com.volvo.tisp.courier</groupId>
      <artifactId>courier-service-api</artifactId>
    </dependency>
    <dependency>
      <groupId>com.volvo.tisp.courier</groupId>
      <artifactId>courier-service-jms-client</artifactId>
    </dependency>

    <dependency>
      <groupId>com.volvo.tisp.framework</groupId>
      <artifactId>tisp-framework-starter-artemis</artifactId>
    </dependency>

    <dependency>
      <groupId>commons-codec</groupId>
      <artifactId>commons-codec</artifactId>
      <version>1.11</version>
    </dependency>
    <dependency>
      <groupId>commons-io</groupId>
      <artifactId>commons-io</artifactId>
    </dependency>
    <dependency>
      <groupId>org.jsondoc</groupId>
      <artifactId>jsondoc-core</artifactId>
      <version>${org.json.doc.version}</version>
    </dependency>
    <dependency>
      <groupId>org.jsondoc</groupId>
      <artifactId>jsondoc-ui</artifactId>
      <version>${org.json.doc.version}</version>
    </dependency>
    <dependency>
      <groupId>org.jsondoc</groupId>
      <artifactId>jsondoc-springmvc</artifactId>
      <version>${org.json.doc.version}</version>
    </dependency>
    <dependency>
      <groupId>com.amazonaws</groupId>
      <artifactId>aws-java-sdk-s3</artifactId>
      <version>1.12.713</version>
    </dependency>
    <dependency>
      <groupId>org.bouncycastle</groupId>
      <artifactId>bcprov-jdk15on</artifactId>
      <version>${bouncycastle.version}</version>
    </dependency>
    <dependency>
      <groupId>org.bouncycastle</groupId>
      <artifactId>bcpkix-jdk15on</artifactId>
      <version>${bouncycastle.version}</version>
    </dependency>
    <dependency>
      <groupId>org.digidoc4j.dss</groupId>
      <artifactId>dss-asic-common</artifactId>
      <version>${dss.doc.version}</version>
    </dependency>
    <dependency>
      <groupId>org.digidoc4j.dss</groupId>
      <artifactId>dss-asic-cades</artifactId>
      <version>${dss.doc.version}</version>
    </dependency>
    <dependency>
      <groupId>org.digidoc4j.dss</groupId>
      <artifactId>dss-jaxb-parsers</artifactId>
      <version>${dss.doc.version}</version>
    </dependency>
    <dependency>
      <groupId>org.digidoc4j.dss</groupId>
      <artifactId>dss-enumerations</artifactId>
      <version>${dss.doc.version}</version>
    </dependency>
    <dependency>
      <groupId>org.digidoc4j.dss</groupId>
      <artifactId>dss-spi</artifactId>
      <version>${dss.doc.version}</version>
    </dependency>
    <dependency>
      <groupId>org.digidoc4j.dss</groupId>
      <artifactId>dss-utils</artifactId>
      <version>${dss.doc.version}</version>
    </dependency>
    <dependency>
      <groupId>org.digidoc4j.dss</groupId>
      <artifactId>dss-detailed-report-jaxb</artifactId>
      <version>${dss.doc.version}</version>
    </dependency>
    <dependency>
      <groupId>org.digidoc4j.dss</groupId>
      <artifactId>dss-document</artifactId>
      <version>${dss.doc.version}</version>
    </dependency>
    <dependency>
      <groupId>org.digidoc4j.dss</groupId>
      <artifactId>dss-cades</artifactId>
      <version>${dss.doc.version}</version>
    </dependency>
    <dependency>
      <groupId>org.digidoc4j.dss</groupId>
      <artifactId>dss-utils-apache-commons</artifactId>
      <version>${dss.doc.version}</version>
    </dependency>
    <dependency>
      <groupId>jakarta.ws.rs</groupId>
      <artifactId>jakarta.ws.rs-api</artifactId>
    </dependency>
    <dependency>
      <groupId>com.amazonaws</groupId>
      <artifactId>aws-java-sdk-sts</artifactId>
      <version>1.12.713</version>
    </dependency>
    <dependency>
      <groupId>com.volvo.tisp.suc</groupId>
      <artifactId>sems-usage-client-api</artifactId>
    </dependency>
    <dependency>
      <groupId>com.volvo.tisp.suc</groupId>
      <artifactId>sems-usage-client-impl-http</artifactId>
    </dependency>
    <!-- SEMS lib -->
    <dependency>
      <groupId>com.volvo.tisp.semslib</groupId>
      <artifactId>sems-lib-sems-utils</artifactId>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-engine</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-junit-jupiter</artifactId>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <!-- +=============================================== -->
  <!-- | Section 3: Build plug-ins -->
  <!-- +=============================================== -->
  <build>
    <plugins>
      <plugin>
        <!-- spring-boot plugin enables: mvn spring-boot:run -->
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
      </plugin>
    </plugins>
  </build>

  <!-- +=============================================== -->
  <!-- | Section 4: Profiles -->
  <!-- +=============================================== -->
  <profiles>
    <profile>
      <!-- To run application locally with local configurations, Need Wiremock
				server, mongo server, AMQ server ..etc running in local machine only -->
      <id>local-boot</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-antrun-plugin</artifactId>
            <version>1.1</version>
            <executions>
              <execution>
                <id>copy-resources</id>
                <goals>
                  <goal>run</goal>
                </goals>
                <phase>process-resources</phase>
                <configuration>
                  <tasks>
                    <echo>copying spring boot jar/env.properties.template to
											required place</echo>
                    <copy file="../deployable-assembly/src/main/content/jar/default/env.properties.template" tofile="${basedir}/target/env.properties"/>
                  </tasks>
                </configuration>
              </execution>
            </executions>
          </plugin>
          <!-- To run app using spring-boot plugin, usage : mvn clean spring-boot:run.
						Run the same from DOS-CMD, we have some problems with Cygwin -->
          <plugin>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-maven-plugin</artifactId>
            <configuration>
              <!-- Passing required boot properties -->
              <jvmArguments>-Xdebug
								-Xrunjdwp:server=y,transport=dt_socket,address=8729,suspend=n
								-DVGTSOLUTION=local
								-DVGTSITE=local
								-DVGTENVID=local
								-DVGTENVIRONMENT=local
								-DVGTZONE=dev
								-DVGTCONFIGFILE=${basedir}/target/env.properties
								-DVGTLOGDIR=C:/tmp/vgt/logs/local-boot/${component.long-name}
								-DmainClass=${mainClass}
								-Dmain.config.class=${component.main-class}
								-DVGTCOMPSHORTNAME=${component.short-name}
								-DVGTCOMPLONGNAME=${component.long-name}</jvmArguments>
            </configuration>
            <executions>
              <execution>
                <goals>
                  <goal>run</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>
</project>
