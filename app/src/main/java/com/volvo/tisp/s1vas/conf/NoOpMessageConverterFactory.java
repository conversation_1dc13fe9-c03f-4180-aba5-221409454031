package com.volvo.tisp.s1vas.conf;

import org.springframework.jms.support.converter.MessageConverter;
import org.springframework.jms.support.converter.SimpleMessageConverter;

/**
 * No-operation implementation of MessageConverterFactory for when <PERSON><PERSON> is disabled.
 * This allows the application to run without JMS connectivity in local development.
 */
public class NoOpMessageConverterFactory {

    public MessageConverter createMessageConverter() {
        return new SimpleMessageConverter();
    }
    
    @Override
    public String toString() {
        return "NoOpMessageConverterFactory(JMS disabled)";
    }
}
