package com.volvo.tisp.s1vas.validator;

import com.volvo.tisp.semsc.client.SemsSecurityClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class TokenValidator {

  private final Logger LOGGER = LoggerFactory.getLogger(getClass());

  @Autowired SemsSecurityClient semsSecurityClient;

  public boolean validateToken(String token) {
    LOGGER.info("Inside S1VAS method TokenValidator.validateToken() started");
    return Boolean.valueOf(semsSecurityClient.isValidToken(token).join());
  }
}
