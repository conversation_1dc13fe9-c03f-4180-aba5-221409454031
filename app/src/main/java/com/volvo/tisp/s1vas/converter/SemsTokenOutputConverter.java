package com.volvo.tisp.s1vas.converter;

import com.volvo.tisp.s1vas.util.Sems1VehicleConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

@Component
public class SemsTokenOutputConverter {

  private static final Logger LOGGER = LoggerFactory.getLogger(SemsTokenOutputConverter.class);

  public ResponseEntity<String> convertResponse(String token) {
    if (null != token) {
      LOGGER.debug("SemsTokenOutputConverter::convertResponse {}", token);
      return ResponseEntity.status(HttpStatus.OK)
          .header(Sems1VehicleConstants.AUTH_HEADER_NAME, token)
          .body(token);
    } else {
      LOGGER.debug("SemsTokenOutputConverter::convertResponse else part {}", token);
      return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(null);
    }
  }

  public String convert(String token) {
    return token;
  }
}
