package com.volvo.tisp.s1vas.domain;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.volvo.tisp.courier.ChoreJmsClient;
import com.volvo.tisp.identifier.WorkflowIdentifier;
import com.volvo.tisp.s1vas.util.ConfigUtil;
import com.volvo.tisp.s1vas.util.Sems1VehicleConstants;
import com.wirelesscar.config.Config;
import com.wirelesscar.courier.data.courier._1_1.Chore;
import com.wirelesscar.courier.data.courier._1_1.Recipient;
import com.wirelesscar.courier.data.courier._1_1.RecipientType;
import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class VehicleVGCSCourierManagerImpl {

  private final Logger LOG = LoggerFactory.getLogger(getClass());
  private static final String CHORE_TYPE_SEMS_FIRMWARE_NOT_AVAILABLE =
      "sems1-vehicle-api-server-Firmware-NotAvailable";
  private static final String CHORE_TYPE_SEMS_AWS_FAILED = "voss-intranet-aws-failed-chore";
  private static final String CHORE_TYPE_SEMS_UPDATE_RESPONSE_FAILED =
      "sems1-update-response-failed-chore";
  private static final String CHORE_TYPE_SEMS_OCSP_FAILED = "sems1-ocsp-failed-chore";
  private static final Gson GSON = new GsonBuilder().setPrettyPrinting().create();

  @Autowired ChoreJmsClient choreJmsClientImpl;

  @Autowired private Config config;

  public void handleCourierNotification(String vin, String threshold) {
    try {
      Chore choreObj = createChoreObj(vin, threshold);
      emailReport(choreObj);
    } catch (Exception e) {
      LOG.error("createChoreObj and emailReport Exception:{}", e.getMessage());
      LOG.error("createChoreObj and emailReport Exception:{}", Arrays.toString(e.getStackTrace()));
    }
  }

  public CompletableFuture<Void> sendEmailForWrongFW(String firmwareName, String primaryId) {
    LOG.debug(
        "Sending email for FW not available {} and vehicle in portal {}", firmwareName, primaryId);
    try {
      Date date = new Date();
      String timeStamp = (new Timestamp(date.getTime())).toString();
      Chore chore = createChoreForFileNotAvailable(primaryId, firmwareName, timeStamp);
      Optional<Boolean> flag = config.getBoolean(Sems1VehicleConstants.COURIER_SERVICE_FLAG);
      if (flag.isPresent()) {
        var subscribers = choreJmsClientImpl.send(chore);
        LOG.debug("No. of subscribers: {}", subscribers);
      }
      return CompletableFuture.completedFuture(null);
    } catch (Exception e) {
      LOG.error(
          "Exception while sending email for file wrong in OTA in VehicleVGCSCourierManagerImpl ",
          e);
    }
    return CompletableFuture.completedFuture(null);
  }

  private Chore createChoreForFileNotAvailable(
      String primaryId, String firmwareName, String timeStamp) {
    Chore chore = new Chore();
    chore.setTemplateData(populateDataForNotAvailableFWEmail(primaryId, firmwareName, timeStamp));
    chore.setType(CHORE_TYPE_SEMS_FIRMWARE_NOT_AVAILABLE);
    chore.setWorkflowIdentifier(WorkflowIdentifier.create().toString());
    chore.getRecipients().add(prepareRecipient());
    return chore;
  }

  private String populateDataForNotAvailableFWEmail(
      String primaryId, String firmwareName, String timeStamp) {
    Map<String, String> templateData = new HashMap<>();
    templateData.put(Sems1VehicleConstants.PRIMARY_ID, primaryId);
    templateData.put(Sems1VehicleConstants.FIRMWARE_NAME, firmwareName);
    templateData.put(Sems1VehicleConstants.TIMESTAMP, timeStamp);
    templateData.put(
        Sems1VehicleConstants.ENVIRONMENT, config.getEnvironmentId().toUpperCase(Locale.ENGLISH));
    return GSON.toJson(templateData);
  }

  private Chore createChoreObj(String vin, String threshold) throws Exception {
    Chore chore = new Chore();
    chore.getRecipients().add(prepareRecipient());
    chore.setType(Sems1VehicleConstants.SEMS_OVERVIEW);
    chore.setTemplateData(prepareMailTemplateData(vin, threshold));
    chore.setWorkflowIdentifier(WorkflowIdentifier.create().toString());
    return chore;
  }

  private void emailReport(Chore choreObj) {
    LOG.debug("EmailCustomerReport :: Courier Service request method:: Start");
    try {
      var subscribers = choreJmsClientImpl.send(choreObj);
      LOG.debug("No. of subscribers: {}", subscribers);
      LOG.debug("EmailCustomerReport :: Courier Service request method:: End");
    } catch (Exception e) {
      LOG.error("Exception while sending email Threshold Reached:{} ", e.getMessage());
    }
  }

  private Recipient prepareRecipient() {
    LOG.debug("prepareRecipient method :: Start");
    Recipient choreRecipient = new Recipient();
    choreRecipient.setType(RecipientType.EMAIL);
    choreRecipient.setTimezone(Sems1VehicleConstants.EMAIL_TIMEZONE);
    choreRecipient.setValue(Sems1VehicleConstants.SEMS_EMAIL);
    choreRecipient.setLocale(Locale.ENGLISH.toString());
    return choreRecipient;
  }

  private String prepareMailTemplateData(String vin, String thrshould) throws Exception {
    LOG.debug("prepareMailTemplateData method :: Start");
    Map<String, String> templateObj = new HashMap<>();
    if (Objects.nonNull(thrshould)
        && thrshould.equalsIgnoreCase(Sems1VehicleConstants.REQUEST_THRESHOLD)) {
      templateObj.put(Sems1VehicleConstants.SUBJECT, "Alert Notification-Request");
      templateObj.put(
          Sems1VehicleConstants.BODY,
          " OTA requests for the Vehicle :" + vin + " has reached the threshold.");
    } else if (Objects.nonNull(thrshould)
        && thrshould.equalsIgnoreCase(Sems1VehicleConstants.DOWNLOAD_THRESHOLD)) {
      templateObj.put(Sems1VehicleConstants.SUBJECT, "Alert Notification-Download");
      templateObj.put(
          Sems1VehicleConstants.BODY, "Download Size Limit has reached for Vehicle :" + vin);
    }
    return GSON.toJson(templateObj);
  }

  public void sendEmailForAWSFailure(
      String externalSystem,
      String packageName,
      String packageType,
      String tenant,
      String timeStamp,
      String operation) {
    LOG.debug(
        "Sending email for AWS failure for the package Name -> {}, package Type -> {}, tenant, -> {}, operation -> {} external system -> {}",
        packageName,
        packageType,
        tenant,
        operation,
        externalSystem);
    try {
      Chore chore =
          createChoreForAWSFailure(
              externalSystem, packageName, packageType, tenant, timeStamp, operation);
      if (ConfigUtil.getCourierServiceFlag()) {
        var subscribers = choreJmsClientImpl.send(chore);
        LOG.debug("No. of subscribers: {}", subscribers);
      }
    } catch (Exception e) {
      LOG.error(
          "Exception while sending email for file upload failure in VehicleVGCSCourierManagerImpl ",
          e);
    }
  }

  private Chore createChoreForAWSFailure(
      String externalSystem,
      String packageName,
      String packageType,
      String tenant,
      String timeStamp,
      String operation) {
    Chore chore = new Chore();
    chore.setTemplateData(
        populateDataForAWSFailureEmail(
            externalSystem, packageName, packageType, tenant, timeStamp, operation));
    chore.setType(CHORE_TYPE_SEMS_AWS_FAILED);
    chore.setWorkflowIdentifier(WorkflowIdentifier.create().toString());
    chore.getRecipients().add(prepareRecipient());
    return chore;
  }

  private String populateDataForAWSFailureEmail(
      String externalSystem,
      String packageName,
      String packageType,
      String tenant,
      String timeStamp,
      String operation) {
    Map<String, String> templateData = new HashMap<>();
    templateData.put(Sems1VehicleConstants.EXTERNAL_SYSTEM, externalSystem);
    templateData.put(Sems1VehicleConstants.PACKAGE_NAME, packageName);
    templateData.put(Sems1VehicleConstants.PACKAGE_TYPE, packageType);
    templateData.put(Sems1VehicleConstants.TENANT, tenant);
    templateData.put(Sems1VehicleConstants.OPERATION, operation);
    templateData.put(Sems1VehicleConstants.TIMESTAMP, timeStamp);
    templateData.put(Sems1VehicleConstants.ENVIRONMENT, ConfigUtil.getEnvironment());
    return GSON.toJson(templateData);
  }

  public void sendEmailForUpdateResponseFailure(String vin, String timeStamp) {
    LOG.info("Sending email for Update Response failure for the vin -> {}", vin);
    try {
      Chore chore = createChoreForUpdateResponseFailure(vin, timeStamp);
      if (ConfigUtil.getCourierServiceFlag()) {
        var subscribers = choreJmsClientImpl.send(chore);
        LOG.debug("No. of subscribers: {}", subscribers);
      }
    } catch (Exception e) {
      LOG.error(
          "Exception while sending email for UpdateResponse failure in VehicleVGCSCourierManagerImpl ",
          e);
    }
  }

  private Chore createChoreForUpdateResponseFailure(String vin, String timeStamp) {
    Chore chore = new Chore();
    chore.setTemplateData(populateDataForUpdateResponseFailureEmail(vin, timeStamp));
    chore.setType(CHORE_TYPE_SEMS_UPDATE_RESPONSE_FAILED);
    chore.setWorkflowIdentifier(WorkflowIdentifier.create().toString());
    chore.getRecipients().add(prepareRecipient());
    return chore;
  }

  private String populateDataForUpdateResponseFailureEmail(String vin, String timeStamp) {
    Map<String, String> templateData = new HashMap<>();
    templateData.put(Sems1VehicleConstants.VIN, vin);
    templateData.put(Sems1VehicleConstants.TIMESTAMP, timeStamp);
    templateData.put(Sems1VehicleConstants.ENVIRONMENT, ConfigUtil.getEnvironment());
    return GSON.toJson(templateData);
  }

  public void sendEmailForOCSPFailure(String vin, String timeStamp) {
    LOG.debug("Sending email for OCSP failure for the vin -> {}", vin);
    try {
      Chore chore = createChoreForOCSPFailure(vin, timeStamp);
      if (ConfigUtil.getCourierServiceFlag()) {
        var subscribers = choreJmsClientImpl.send(chore);
        LOG.debug("No. of subscribers: {}", subscribers);
      }
    } catch (Exception e) {
      LOG.error(
          "Exception while sending email for OCSP failure in VehicleVGCSCourierManagerImpl ", e);
    }
  }

  private Chore createChoreForOCSPFailure(String vin, String timeStamp) {
    Chore chore = new Chore();
    chore.setTemplateData(populateDataForOCSPFailureEmail(vin, timeStamp));
    chore.setType(CHORE_TYPE_SEMS_OCSP_FAILED);
    chore.setWorkflowIdentifier(WorkflowIdentifier.create().toString());
    chore.getRecipients().add(prepareRecipient());
    return chore;
  }

  private String populateDataForOCSPFailureEmail(String vin, String timeStamp) {
    Map<String, String> templateData = new HashMap<>();
    templateData.put(Sems1VehicleConstants.VIN, vin);
    templateData.put(Sems1VehicleConstants.TIMESTAMP, timeStamp);
    templateData.put(Sems1VehicleConstants.ENVIRONMENT, ConfigUtil.getEnvironment());
    return GSON.toJson(templateData);
  }
}
