package com.volvo.tisp.s1vas.domain;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.volvo.tisp.s1vas.util.Sems1MessageConstants;
import com.volvo.tisp.s1vas.util.Sems1VehicleConstants;
import com.volvo.tisp.s1vas.util.ThresholdUtil;
import com.volvo.tisp.sem1core.client.PentaOTARequestClient;
import com.volvo.tisp.sem1core.client.dto.InstallationPackageResponseDTO;
import com.volvo.tisp.sem1core.client.dto.PentaUpdateRequestDTO;
import com.volvo.tisp.sem1core.client.dto.UpdateRequestDTO;
import com.volvo.tisp.semsc.client.SemsSecurityClient;
import com.volvo.tisp.semslib.exceptions.*;
import com.wirelesscar.config.Config;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.concurrent.ExecutionException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClientResponseException;

@Component
public class SemsOTARequestManagerImplBean {

  private final Logger LOGGER = LoggerFactory.getLogger(getClass());

  @Autowired SemsSecurityClient semsSecurityClient;

  @Autowired ThresholdUtil thresholdUtil;

  @Autowired private com.volvo.tisp.sem1core.client.OTARequestClient otaSem1Client;

  @Autowired Config config;

  @Autowired private PentaOTARequestClient pentaOTARequestClient;

  private final ObjectMapper mapper = new ObjectMapper();

  public CompletableFuture<List<InstallationPackageResponseDTO>> handleOTAInputRequest(
      UpdateRequestDTO updateRequestDTO, long startTime, String tenant) {
    if (config.getString(Sems1VehicleConstants.ENABLE_RETRY_AFTER).isPresent()
        && Boolean.parseBoolean(config.getString(Sems1VehicleConstants.ENABLE_RETRY_AFTER).get())
        && (!(Sems1VehicleConstants.VCE_TENANT.equalsIgnoreCase(tenant)
            || Sems1VehicleConstants.PENTA_TENANT.equalsIgnoreCase(tenant)))) {
      return thresholdUtil
          .handleOTARequestThreshold(
              tenant, Sems1VehicleConstants.SEM_ONE, updateRequestDTO.getPrimaryId())
          .thenCompose(error -> updateOTARequest(updateRequestDTO, startTime))
          .thenApply(
              responseDTOs -> {
                if (null != responseDTOs) {
                  return thresholdUtil.handleResponseList(responseDTOs);
                } else {
                  return null;
                }
              })
          .thenCompose(
              responseDTOs ->
                  thresholdUtil.handleOTADataThreshold(
                      tenant, responseDTOs, updateRequestDTO.getPrimaryId()))
          .exceptionally(
              throwable -> {
                if (throwable.getCause() instanceof InvalidCredentialsException
                    || throwable.getCause() instanceof WebClientResponseException.Unauthorized) {
                  LOGGER.error("Token Exception occurred");
                  throw new InvalidCredentialsException(
                      Sems1MessageConstants.TOKEN_NOT_VALID_MESSAGE);
                } else if (throwable.getCause() instanceof ServiceUnavailableException) {
                  LOGGER.error(
                      "Error code 200, Retry after next day {} ", updateRequestDTO.getPrimaryId());
                  throw new ServiceUnavailableException(
                      Sems1MessageConstants.VEHICLE_DATA_MESSAGE + updateRequestDTO.getPrimaryId());
                } else if (throwable.getCause() instanceof NotFoundException
                    || throwable.getCause() instanceof WebClientResponseException.NotFound) {
                  LOGGER.error(
                      "The data for Primary id {} is not found", updateRequestDTO.getPrimaryId());
                  throw new NotFoundException(
                      Sems1MessageConstants.VEHICLE_DATA_MESSAGE + updateRequestDTO.getPrimaryId());
                } else if (throwable.getCause() instanceof IllegalArgumentException) {
                  throw new BadRequestException("Illegal arguments");
                } else if (throwable.getCause() instanceof InterruptedException
                    || throwable.getCause() instanceof ExecutionException) {
                  throw new InvalidCredentialsException(
                      "SemsOTARequestManagerImplBean.handleOTAInputRequest() Token conversion exception");
                } else if (throwable.getCause() instanceof TooManyRequestsException
                    || throwable.getCause() instanceof WebClientResponseException.TooManyRequests) {
                  throw new BadRequestException(Sems1MessageConstants.TOO_MANY_REQUEST_MESSAGE);
                } else {
                  throw new InternalServerErrorException(
                      Sems1MessageConstants.INTERNAL_ERROR_MESSAGE);
                }
              });
    } else {
      return updateOTARequest(updateRequestDTO, startTime)
          .exceptionally(
              throwable -> {
                if (throwable.getCause() instanceof InvalidCredentialsException
                    || throwable.getCause() instanceof WebClientResponseException.Unauthorized) {
                  LOGGER.error("Token Exception occurred");
                  throw new InvalidCredentialsException(
                      Sems1MessageConstants.TOKEN_NOT_VALID_MESSAGE);
                } else if (throwable.getCause() instanceof ServiceUnavailableException) {
                  LOGGER.error(
                      "Error code 200, Retry after next day {} ", updateRequestDTO.getPrimaryId());
                  throw new ServiceUnavailableException(
                      Sems1MessageConstants.VEHICLE_DATA_MESSAGE + updateRequestDTO.getPrimaryId());
                } else if (throwable.getCause() instanceof NotFoundException
                    || throwable.getCause() instanceof WebClientResponseException.NotFound) {
                  LOGGER.error(
                      "The data for Primary id {} is not found", updateRequestDTO.getPrimaryId());
                  throw new NotFoundException(
                      Sems1MessageConstants.VEHICLE_DATA_MESSAGE + updateRequestDTO.getPrimaryId());
                } else if (throwable.getCause() instanceof IllegalArgumentException) {
                  throw new BadRequestException("Illegal arguments");
                } else if (throwable.getCause() instanceof InterruptedException
                    || throwable.getCause() instanceof ExecutionException) {
                  throw new InvalidCredentialsException(
                      "SemsOTARequestManagerImplBean.handleOTAInputRequest() Token conversion exception");
                } else if (throwable.getCause() instanceof TooManyRequestsException
                    || throwable.getCause() instanceof WebClientResponseException.TooManyRequests) {
                  throw new BadRequestException(Sems1MessageConstants.TOO_MANY_REQUEST_MESSAGE);
                } else {
                  throw new InternalServerErrorException(
                      Sems1MessageConstants.INTERNAL_ERROR_MESSAGE);
                }
              });
    }
  }

  public CompletableFuture<List<InstallationPackageResponseDTO>> handleOTAInputRequestV2(
      UpdateRequestDTO updateRequestDTO, long startTime) {

    return updateOTARequest(updateRequestDTO, startTime)
        .thenApply(response -> thresholdUtil.signAppsFw(response))
        .exceptionally(
            throwable -> {
              if (throwable.getCause() instanceof InvalidCredentialsException
                  || throwable.getCause() instanceof WebClientResponseException.Unauthorized) {
                LOGGER.error("handleOTAInputRequestV2 Token Exception occurred");
                throw new InvalidCredentialsException(
                    Sems1MessageConstants.TOKEN_NOT_VALID_MESSAGE);
              } else if (throwable.getCause() instanceof NotFoundException
                  || throwable.getCause() instanceof WebClientResponseException.NotFound) {
                LOGGER.error(
                    "handleOTAInputRequestV2 data for Primary id {} is not found",
                    updateRequestDTO.getPrimaryId());
                throw new NotFoundException(
                    Sems1MessageConstants.VEHICLE_DATA_MESSAGE + updateRequestDTO.getPrimaryId());
              } else if (throwable.getCause() instanceof IllegalArgumentException) {
                throw new BadRequestException("handleOTAInputRequestV2 Illegal arguments");
              } else if (throwable.getCause() instanceof InterruptedException
                  || throwable.getCause() instanceof ExecutionException) {
                throw new InvalidCredentialsException(
                    "SemsOTARequestManagerImplBean.handleOTAInputRequestV2() Token conversion exception");
              } else if (throwable.getCause() instanceof TooManyRequestsException
                  || throwable.getCause() instanceof WebClientResponseException.TooManyRequests) {
                throw new BadRequestException(Sems1MessageConstants.TOO_MANY_REQUEST_MESSAGE);
              } else {
                throw new InternalServerErrorException(
                    Sems1MessageConstants.INTERNAL_ERROR_MESSAGE);
              }
            });
  }

  public CompletableFuture<Boolean> validateToken(String token) {
    LOGGER.info("Inside SemsOTARequestManagerImplBean.validateToken S1VAS started");
    return semsSecurityClient
        .isValidToken(token)
        .thenApply(
            tok -> {
              if (!StringUtils.isBlank(tok) && Boolean.parseBoolean(tok)) {
                return true;
              } else {
                return false;
              }
            });
  }

  private CompletableFuture<List<InstallationPackageResponseDTO>> updateOTARequest(
      com.volvo.tisp.sem1core.client.dto.UpdateRequestDTO updateRequestDTO, long startTime) {
    LOGGER.debug("Entered into Sems1-core-server core with Request:{}", updateRequestDTO);
    return otaSem1Client
        .updateOTASem1(convertToSem1(updateRequestDTO))
        .thenCompose(resp -> printLogs(resp, startTime, updateRequestDTO.getPrimaryId()));
  }

  private com.volvo.tisp.sem1core.client.dto.UpdateRequestDTO convertToSem1(
      UpdateRequestDTO updateRequestDTO) {
    com.volvo.tisp.sem1core.client.dto.UpdateRequestDTO updateRequestSem1DTO =
        new com.volvo.tisp.sem1core.client.dto.UpdateRequestDTO();
    try {
      updateRequestSem1DTO = mapper.convertValue(updateRequestDTO, new TypeReference<>() {});
    } catch (IllegalArgumentException exep) {
      LOGGER.error("unable to convert to response object {} ", updateRequestDTO);
      throw new IllegalArgumentException("error while mapping to pojo object ");
    }
    return updateRequestSem1DTO;
  }

  private CompletionStage<List<InstallationPackageResponseDTO>> printLogs(
      List<com.volvo.tisp.sem1core.client.dto.InstallationPackageResponseDTO> resp,
      long startTime,
      String primaryId) {
    List<InstallationPackageResponseDTO> response;
    LOGGER.info(
        "/v1/update sems1-core-server for Vehicle :{} time taken to processRequest:{} SemsOTARequestManagerImplBean.updateResponse:{}",
        primaryId,
        (System.currentTimeMillis() - startTime),
        resp);
    try {
      response = mapper.convertValue(resp, new TypeReference<>() {});
    } catch (IllegalArgumentException exep) {
      LOGGER.error("unable to convert to response object {} ", resp);
      throw new IllegalArgumentException("error while mapping to pojo object ");
    }
    return CompletableFuture.completedFuture(response);
  }

  public CompletableFuture<List<InstallationPackageResponseDTO>> handlePentaOTAInputRequestV1(
      PentaUpdateRequestDTO pentaUpdateRequestDTO, long startTime) {
    return updatePentaOTARequest(pentaUpdateRequestDTO, startTime)
        .thenApply(response -> thresholdUtil.signAppsFw(response))
        .exceptionally(
            throwable -> {
              if (throwable.getCause() instanceof InvalidCredentialsException
                  || throwable.getCause() instanceof WebClientResponseException.Unauthorized) {
                LOGGER.error("handlePentaOTAInputRequestV1 Token Exception occurred");
                throw new InvalidCredentialsException(
                    Sems1MessageConstants.TOKEN_NOT_VALID_MESSAGE);
              } else if (throwable.getCause() instanceof NotFoundException
                  || throwable.getCause() instanceof WebClientResponseException.NotFound) {
                LOGGER.error(
                    "handlePentaOTAInputRequestV1 data for Primary id {} is not found",
                    pentaUpdateRequestDTO.getPrimaryId());
                throw new NotFoundException(
                    Sems1MessageConstants.VEHICLE_DATA_MESSAGE
                        + pentaUpdateRequestDTO.getPrimaryId());
              } else if (throwable.getCause() instanceof IllegalArgumentException) {
                throw new BadRequestException("handlePentaOTAInputRequestV1 Illegal arguments");
              } else if (throwable.getCause() instanceof InterruptedException
                  || throwable.getCause() instanceof ExecutionException) {
                throw new InvalidCredentialsException(
                    "SemsOTARequestManagerImplBean.handlePentaOTAInputRequestV1() Token conversion exception");
              } else if (throwable.getCause() instanceof TooManyRequestsException
                  || throwable.getCause() instanceof WebClientResponseException.TooManyRequests) {
                throw new BadRequestException(Sems1MessageConstants.TOO_MANY_REQUEST_MESSAGE);
              } else {
                throw new InternalServerErrorException(
                    Sems1MessageConstants.INTERNAL_ERROR_MESSAGE);
              }
            });
  }

  private CompletableFuture<List<InstallationPackageResponseDTO>> updatePentaOTARequest(
      PentaUpdateRequestDTO pentaUpdateRequestDTO, long startTime) {
    LOGGER.debug(
        "Entered into SemsOTARequestManagerImplBean.updatePentaOTARequest()",
        pentaUpdateRequestDTO);
    return pentaOTARequestClient
        .updateOTAForPenta(convertToPentaTad1(pentaUpdateRequestDTO))
        .thenCompose(resp -> printLogs(resp, startTime, pentaUpdateRequestDTO.getPrimaryId()));
  }

  private PentaUpdateRequestDTO convertToPentaTad1(PentaUpdateRequestDTO pentaUpdateRequestDTO) {
    PentaUpdateRequestDTO updateRequestSem1DTO;
    try {
      updateRequestSem1DTO = mapper.convertValue(pentaUpdateRequestDTO, new TypeReference<>() {});
    } catch (IllegalArgumentException exep) {
      LOGGER.error("Unable to convert to response object {} ", pentaUpdateRequestDTO);
      throw new IllegalArgumentException("Error while mapping to pojo object ");
    }
    return updateRequestSem1DTO;
  }
}
