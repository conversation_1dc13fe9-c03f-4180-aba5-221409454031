package com.volvo.tisp.s1vas.util;

public final class Sems1VehicleConstants {
  private Sems1VehicleConstants() {}

  public static final String VCE_TENANT = "vce";
  public static final String NA_TENANT = "trucks_na";
  public static final String PENTA_TENANT = "penta";
  public static final String AUTHORIZATION = "Authorization";
  public static final String USERNAME = "Username";
  public static final String PASSWORD = "Password";
  public static final String ENABLED = "ENABLED";
  public static final String VIN = "vin";
  public static final String TENANT = "tenant";
  public static final String TIMESTAMP = "timestamp";
  public static final String ENVIRONMENT = "environment";
  public static final String OPERATION = "operation";
  public static final String PACKAGE_TYPE = "packageType";
  public static final String PACKAGE_NAME = "packageName";
  public static final String EXTERNAL_SYSTEM = "externalSystem";
  public static final String PRIMARY_ID = "primaryId";

  public static final String ID = "id";
  public static final String FIRMWARE_NAME = "firmwareName";
  public static final String SUBJECT = "subject";
  public static final String BODY = "body";
  public static final String COURIER_SERVICE_FLAG = "courier.flag";
  public static final String REQUEST_THRESHOLD = "Request";
  public static final String DOWNLOAD_THRESHOLD = "Download";
  public static final String EMAIL_TIMEZONE = "UTC";
  public static final String SEMS_OVERVIEW = "SemsAlertsEMailTemplate";
  public static final String SEMS_EMAIL = "<EMAIL>";
  public static final String AUTH_HEADER_NAME = "X-AUTH-TOKEN";
  public static final String AUTH_HEADER_TENANT = "X-AUTH-TENANT";
  // AWS
  public static final String AWS_BUCKET = "bucket.name";
  public static final String CLOUDFRONT = "cloudfront.url";
  public static final String MAP_PACKAGE = "MAP_PACKAGE";
  public static final String FILENAME = "filename";
  public static final String SKIP_BYTES = "X-SKIP-BYTES";
  public static final String FILESHARE1_PATH = "fileshare.path";
  public static final String TOKEN_GENERATE_URL = "invalidtoken.url";
  public static final String NA_TOKEN_GENERATE_URL = "natoken.url";
  public static final String AUTH_HEADER_URL = "X-AUTH-URL";

  public static final String FILESHARE_PING_SUCCESS = "Looking good : ";
  public static final String FILESHARE_PING_FAILURE =
      "Configured fileshare path is not reachable : ";
  public static final String OTA_THRESHOLD_ERROR = "Error code 503, Retry-After=3600";
  public static final String PRESIGN_DOWNLOAD_S3 = "presigndownload.enable";
  public static final String SEM_ONE = "sem1";
  public static final String SEM_ONE_FOLDER = "SEM1/";
  public static final String OTA_DAY_THRESHOLD = "Error code 200, Retry-After Next Day";
  public static final String ENABLE_RETRY_AFTER = "enableRetryAfter";
  public static final String AUTHENTICATE = "WWW-Authenticate";
  public static final String X_AUTH_TOKEN_REALM = "X-AUTH-TOKEN realm=\"voss\"";
  public static final String CONTENT_TYPE = "Content-Type";
  public static final String CONTENT_DISPOSITION = "Content-disposition";
  public static final String CONTENTTYPE = "ContentType";
  public static final String BINARY_OCTATE = "binary/octet-stream";
  public static final String ATTACHMENT_FILENAME = "attachment; filename=\"";
  public static final String PROPERTY = "property";
  public static final String METADATA_VALUE_NAME = "metadataValueName";
}
