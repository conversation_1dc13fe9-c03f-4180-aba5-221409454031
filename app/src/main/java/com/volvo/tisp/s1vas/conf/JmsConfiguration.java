package com.volvo.tisp.s1vas.conf;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.volvo.tisp.courier.ChoreJmsClient;
import com.volvo.tisp.courier.impl.ChoreJmsClientImpl;
import com.volvo.tisp.subscriptionrepository.client.DefaultMessagePublisher;
import com.volvo.tisp.subscriptionrepository.client.MessagePublisher;
import com.wirelesscar.vehicleservice.vehicleserviceconfigstatus.client.VSCSJmsClientV3;
import com.wirelesscar.vehicleservice.vehicleserviceconfigstatus.jms.update.v3.VSCSJmsClientV3_0;
import jakarta.jms.Connection;
import jakarta.jms.ConnectionFactory;
import jakarta.jms.JMSException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jms.core.JmsTemplate;
import org.springframework.jms.support.converter.MessageConverter;
import org.springframework.jms.support.converter.SimpleMessageConverter;

/**
 * Comprehensive JMS Configuration that handles all JMS-related beans
 * and provides no-op implementations when JMS is disabled.
 */
@Configuration
public class JmsConfiguration {

    private static final Logger LOG = LoggerFactory.getLogger(JmsConfiguration.class);

    // ========== JMS ENABLED BEANS ==========

    @Bean
    @ConditionalOnProperty(name = "jms.enabled", havingValue = "true", matchIfMissing = false)
    public ChoreJmsClient choreJmsClient(DefaultMessagePublisher.Builder builder) {
        LOG.info("Creating real ChoreJmsClient - JMS is enabled");
        return new ChoreJmsClientImpl(builder);
    }

    @Bean
    @ConditionalOnProperty(name = "jms.enabled", havingValue = "true", matchIfMissing = false)
    public VSCSJmsClientV3 jmsClientV3(JmsTemplate jmsTemplate, ObjectMapper objectMapper) {
        LOG.info("Creating real VSCSJmsClientV3 - JMS is enabled");
        return new VSCSJmsClientV3_0(jmsTemplate, objectMapper);
    }

    @Bean
    @ConditionalOnProperty(name = "jms.enabled", havingValue = "true", matchIfMissing = false)
    public ChoreJmsClient semsCoreChoreJmsClient(MessagePublisher.Builder builder) {
        LOG.info("Creating real ChoreJmsClient for SemsCore - JMS is enabled");
        return new ChoreJmsClientImpl(builder);
    }



    // ========== JMS DISABLED BEANS (NO-OP IMPLEMENTATIONS) ==========

    @Bean
    @ConditionalOnProperty(name = "jms.enabled", havingValue = "false", matchIfMissing = true)
    public ChoreJmsClient noOpChoreJmsClient() {
        LOG.info("Creating NoOp ChoreJmsClient - JMS is disabled");
        return new NoOpChoreJmsClient();
    }

    @Bean
    @ConditionalOnProperty(name = "jms.enabled", havingValue = "false", matchIfMissing = true)
    public ConnectionFactory noOpConnectionFactory() {
        LOG.info("Creating NoOp ConnectionFactory - JMS is disabled");
        return new ConnectionFactory() {
            @Override
            public Connection createConnection() throws JMSException {
                throw new JMSException("JMS is disabled - no-op ConnectionFactory");
            }

            @Override
            public Connection createConnection(String userName, String password) throws JMSException {
                throw new JMSException("JMS is disabled - no-op ConnectionFactory");
            }

            @Override
            public jakarta.jms.JMSContext createContext() {
                throw new RuntimeException("JMS is disabled - no-op ConnectionFactory");
            }

            @Override
            public jakarta.jms.JMSContext createContext(String userName, String password) {
                throw new RuntimeException("JMS is disabled - no-op ConnectionFactory");
            }

            @Override
            public jakarta.jms.JMSContext createContext(String userName, String password, int sessionMode) {
                throw new RuntimeException("JMS is disabled - no-op ConnectionFactory");
            }

            @Override
            public jakarta.jms.JMSContext createContext(int sessionMode) {
                throw new RuntimeException("JMS is disabled - no-op ConnectionFactory");
            }
        };
    }

    @Bean
    @ConditionalOnProperty(name = "jms.enabled", havingValue = "false", matchIfMissing = true)
    public JmsTemplate noOpJmsTemplate(ConnectionFactory noOpConnectionFactory) {
        LOG.info("Creating NoOp JmsTemplate - JMS is disabled");
        JmsTemplate jmsTemplate = new JmsTemplate() {
            @Override
            public void send(String destinationName, org.springframework.jms.core.MessageCreator messageCreator) {
                LOG.debug("NoOp JmsTemplate.send() called - JMS is disabled");
            }

            @Override
            public void convertAndSend(String destinationName, Object message) {
                LOG.debug("NoOp JmsTemplate.convertAndSend() called - JMS is disabled");
            }
        };
        jmsTemplate.setConnectionFactory(noOpConnectionFactory);
        return jmsTemplate;
    }

    @Bean
    @ConditionalOnProperty(name = "jms.enabled", havingValue = "false", matchIfMissing = true)
    public VSCSJmsClientV3 noOpJmsClientV3() {
        LOG.info("Creating NoOp VSCSJmsClientV3 - JMS is disabled");
        return new VSCSJmsClientV3() {
            @Override
            public java.util.concurrent.CompletableFuture<Void> sendServiceItemStatusUpdate(Object message) {
                LOG.debug("NoOp VSCSJmsClientV3.sendServiceItemStatusUpdate() called - JMS is disabled");
                return java.util.concurrent.CompletableFuture.completedFuture(null);
            }
        };
    }



    @Bean(name = "messageConverterFactory")
    @ConditionalOnProperty(name = "jms.enabled", havingValue = "false", matchIfMissing = true)
    public Object messageConverterFactory() {
        LOG.info("Creating NoOp MessageConverterFactory - JMS is disabled");
        try {
            // Try to load the actual class and create a real instance
            Class<?> factoryClass = Class.forName("com.volvo.tisp.framework.jms.support.converter.MessageConverterFactory");
            Object instance = factoryClass.getDeclaredConstructor().newInstance();
            LOG.info("Successfully created real MessageConverterFactory instance");
            return instance;
        } catch (Exception e) {
            LOG.warn("Could not create real MessageConverterFactory, using no-op implementation: {}", e.getMessage());
            // Return a simple object with the required method
            return new Object() {
                public MessageConverter createMessageConverter() {
                    return new SimpleMessageConverter();
                }
                
                @Override
                public String toString() {
                    return "NoOpMessageConverterFactory(JMS disabled)";
                }
            };
        }
    }
}
