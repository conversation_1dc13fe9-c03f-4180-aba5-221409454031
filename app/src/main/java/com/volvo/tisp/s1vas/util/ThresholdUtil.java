package com.volvo.tisp.s1vas.util;

import com.amazonaws.services.s3.AmazonS3;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.volvo.tisp.framework.logging.Retention;
import com.volvo.tisp.s1vas.domain.VehicleVGCSCourierManagerImpl;
import com.volvo.tisp.sem1core.client.dto.InstallationPackageResponseDTO;
import com.volvo.tisp.semslib.exceptions.ServiceUnavailableException;
import com.volvo.tisp.suc.client.RequestDataThresholdClient;
import com.volvo.tisp.suc.client.dto.FilesRequestData;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class ThresholdUtil {
  private static final Logger LOGGER = LoggerFactory.getLogger(ThresholdUtil.class);

  @Autowired SignedAWSUtils signedAWSUtils;

  @Autowired private AmazonS3 amazonS3;

  @Autowired RequestDataThresholdClient requestDataThresholdClient;

  @Autowired VehicleVGCSCourierManagerImpl vehicleVGCSCourierManagerImpl;

  public CompletableFuture<String> handleOTARequestThreshold(String primaryId) {
    return requestDataThresholdClient
        .findRequestThresholdCount(primaryId)
        .thenApply(
            data -> {
              if (null != data
                  && Sems1VehicleConstants.OTA_THRESHOLD_ERROR.equalsIgnoreCase(
                      data.getResponseMessage())) {
                LOGGER.debug(
                    "OTARequestThresholdCount : --> {} OTADataServedDownloadSize : --> {} OTARequestData Threshold message : --> {} "
                        + "Requests count reached to threshold limit for given time",
                    data.getServedRequestCount(),
                    data.getServedDownloadSize(),
                    data.getResponseMessage());
                throw new ServiceUnavailableException(Sems1VehicleConstants.OTA_THRESHOLD_ERROR);
              } else {
                return data.getResponseMessage();
              }
            });
  }

  @SuppressWarnings("FutureReturnValueIgnored")
  public CompletableFuture<List<InstallationPackageResponseDTO>> handleOTADataThreshold(
      String tenant, List<InstallationPackageResponseDTO> installationResponse, String primaryId) {
    List<FilesRequestData> filesList = new ArrayList<>();
    for (InstallationPackageResponseDTO file : installationResponse) {
      if (!Sems1VehicleConstants.MAP_PACKAGE.equalsIgnoreCase(
              file.getInstallationPackageType().name())
          && Sems1VehicleConstants.ENABLED.equalsIgnoreCase(file.getStatus().name())) {
        FilesRequestData filesRequestData = new FilesRequestData();
        filesRequestData.setFileName(file.getName());
        filesRequestData.setFileSize(file.getFileSize());
        filesRequestData.setInstallationPackageType(file.getInstallationPackageType().getValue());
        filesRequestData.setInstallationPackageStatusType(file.getStatus().name());
        filesList.add(filesRequestData);
      }
    }

    if (filesList.size() > 0) {
      return requestDataThresholdClient
          .findDataThresholdCountWithTenantAndSem(
              tenant, Sems1VehicleConstants.SEM_ONE, primaryId, filesList)
          .thenApply(
              data -> {
                if (null != data
                    && Sems1VehicleConstants.OTA_THRESHOLD_ERROR.equalsIgnoreCase(
                        data.getResponseMessage())) {
                  LOGGER.debug(
                      "OTARequestThresholdCount :{} with OTADataServedDownloadSize :{} and OTARequestData Threshold message :{} Data download size reached to data threshold limit for given time",
                      data.getServedRequestCount(),
                      data.getServedDownloadSize(),
                      data.getResponseMessage());
                  throw new ServiceUnavailableException(Sems1VehicleConstants.OTA_THRESHOLD_ERROR);
                } else {
                  return new ArrayList<InstallationPackageResponseDTO>(installationResponse);
                }
              });
    } else {
      List<InstallationPackageResponseDTO> installationPackageResponseDTOListDTO =
          new ArrayList<InstallationPackageResponseDTO>(installationResponse);
      return CompletableFuture.completedFuture(installationPackageResponseDTOListDTO);
    }
  }

  public List<InstallationPackageResponseDTO> signAppsFw(
      List<InstallationPackageResponseDTO> installationPackageList) {
    List<InstallationPackageResponseDTO> installationPackageResponseDTOList = null;

    ObjectMapper mapper = new ObjectMapper();
    try {
      installationPackageResponseDTOList =
          mapper.convertValue(
              installationPackageList,
              new TypeReference<List<InstallationPackageResponseDTO>>() {});
    } catch (IllegalArgumentException exep) {
      LOGGER.error(
          Retention.EXTENDED, "unable to convert to response object {} ", installationPackageList);
      throw new IllegalArgumentException("error while mapping to pojo object ");
    }
    for (InstallationPackageResponseDTO installationPackage : installationPackageResponseDTOList) {
      if (!(com.volvo.tisp.sem1core.client.types.InstallationPackageType.MAP_PACKAGE.equals(
              installationPackage.getInstallationPackageType()))
          && installationPackage
              .getStatus()
              .equals(com.volvo.tisp.sem1core.client.types.InstallationPackageStatusType.ENABLED)
          && StringUtils.isNotEmpty(installationPackage.getUrl())) {
        installationPackage.setUrl(
            signedAWSUtils.signFile(installationPackage.getUrl(), amazonS3).toString());
      }
    }
    return installationPackageResponseDTOList;
  }

  public CompletableFuture<String> handleOTARequestThreshold(
      String tenant, String sem, String primaryId) {
    return requestDataThresholdClient
        .findRequestThresholdWithSEMAndTenant(tenant, sem, primaryId)
        .thenApply(
            data -> {
              if (null != data
                  && Sems1VehicleConstants.OTA_THRESHOLD_ERROR.equalsIgnoreCase(
                      data.getResponseMessage())) {
                LOGGER.debug(
                    Retention.EXTENDED,
                    " For VIN :{} OTARequestThresholdCount : --> {} OTADataServedDownloadSize : --> {} OTARequestData Threshold message : --> {} "
                        + "Requests count reached to threshold limit for given time",
                    primaryId,
                    data.getServedRequestCount(),
                    data.getServedDownloadSize(),
                    data.getResponseMessage());
                throw new ServiceUnavailableException(Sems1VehicleConstants.OTA_THRESHOLD_ERROR);
              } else if (null != data
                  && Sems1VehicleConstants.OTA_DAY_THRESHOLD.equalsIgnoreCase(
                      data.getResponseMessage())) {
                LOGGER.debug(
                    Retention.EXTENDED, " For VIN :{} Daily Threshold has been reached", primaryId);
                vehicleVGCSCourierManagerImpl.handleCourierNotification(
                    primaryId, Sems1VehicleConstants.REQUEST_THRESHOLD);
                throw new ServiceUnavailableException(Sems1VehicleConstants.OTA_DAY_THRESHOLD);
              } else {
                try {
                  return data.getResponseMessage();
                } catch (Exception e) {
                  LOGGER.error(
                      Retention.EXTENDED,
                      " For VIN :{} query returning more than one result, continue to core",
                      primaryId);
                  return primaryId; // NonUniqueResultException: query did not return a unique
                  // result, continue to core
                }
              }
            });
  }

  public List<InstallationPackageResponseDTO> handleResponseList(
      List<InstallationPackageResponseDTO> installationResponse) {
    List<InstallationPackageResponseDTO> installationPackageResponseDTOList = null;

    ObjectMapper mapper = new ObjectMapper();
    try {
      installationPackageResponseDTOList =
          mapper.convertValue(
              installationResponse, new TypeReference<List<InstallationPackageResponseDTO>>() {});
    } catch (IllegalArgumentException exep) {
      LOGGER.error(
          Retention.EXTENDED, "unable to convert to response object {} ", installationResponse);
      throw new IllegalArgumentException("error while mapping to pojo object ");
    }
    return installationPackageResponseDTOList;
  }
}
