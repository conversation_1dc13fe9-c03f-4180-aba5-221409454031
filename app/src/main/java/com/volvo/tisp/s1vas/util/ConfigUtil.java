package com.volvo.tisp.s1vas.util;

import com.volvo.tisp.semslib.exceptions.NotFoundException;
import com.wirelesscar.config.Config;
import com.wirelesscar.config.ConfigFactory;

public class ConfigUtil {

  private static final Config config = ConfigFactory.getConfig();

  private ConfigUtil() {}

  public static String getResponseCertificatePath() {
    return config
        .getString("file.resp.path")
        .orElseThrow(() -> new NotFoundException("Response Certificate not available"));
  }

  public static String getResponseCertificateAlias() {
    return config
        .getString("file.resp.alias")
        .orElseThrow(() -> new NotFoundException("Response Certificate Alias not available"));
  }

  public static String getRootCertificatePath() {
    return config
        .getString("file.root.path")
        .orElseThrow(() -> new NotFoundException("Root Certificate not available"));
  }

  public static String getRootCertificateAlias() {
    return config
        .getString("file.root.alias")
        .orElseThrow(() -> new NotFoundException("Root Certificate Alias not available"));
  }

  public static String getOCSPURL() {
    return config
        .getString("ocsp.url")
        .orElseThrow(() -> new NotFoundException("ocsp.url not available"));
  }

  public static String getRequestSign() {
    return config
        .getString("file.sign")
        .orElseThrow(() -> new NotFoundException("Request Sign Certificate not available"));
  }

  public static String getRequestSignCertificate() {
    return config
        .getString("file.sign.path")
        .orElseThrow(() -> new NotFoundException("Request Sign Certificate not available"));
  }

  public static String getRequestSignAlias() {
    return config
        .getString("file.sign.alias")
        .orElseThrow(() -> new NotFoundException("Request Alias Certificate not available"));
  }

  public static String getKMSKeyId() {
    return config
        .getString("kms.Key.Id")
        .orElseThrow(() -> new NotFoundException("Request KeyId for signing Not available"));
  }

  public static String getKMSKey() {
    return config
        .getString("kms.arn")
        .orElseThrow(() -> new NotFoundException("Request KeyId for signing Not available"));
  }

  public static String getCourierAWS() {
    return config
        .getString("courier.aws")
        .orElseThrow(() -> new NotFoundException("Request AWS courier URL Not available"));
  }

  public static String getEnvironment() {
    return config
        .getString("environment")
        .orElseThrow(() -> new NotFoundException("Request Environment Not available"));
  }

  public static Boolean getCourierServiceFlag() {
    return config
        .getBoolean("courier.flag")
        .orElseThrow(() -> new NotFoundException("Request Courier Service Flag Not available"));
  }
}
