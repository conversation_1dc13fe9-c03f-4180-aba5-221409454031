package com.volvo.tisp.s1vas.rest;

import com.volvo.tisp.framework.logging.Retention;
import com.volvo.tisp.framework.security.annotation.Authentication;
import com.volvo.tisp.s1vas.domain.SecurityGetTokenURLImpl;
import com.volvo.tisp.s1vas.domain.SemsOTARequestManagerImplBean;
import com.volvo.tisp.s1vas.util.Sems1VehicleConstants;
import com.volvo.tisp.sem1core.client.dto.InstallationPackageResponseDTO;
import com.volvo.tisp.sem1core.client.dto.UpdateRequestDTO;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Authentication(required = false)
public class UpdateOTARequestRestHandler {

  private final Logger LOGGER = LoggerFactory.getLogger(getClass());

  @Autowired SemsOTARequestManagerImplBean semsOTARequestManagerImplBean;
  @Autowired SecurityGetTokenURLImpl securityGetTokenURLImpl;

  /*
   * Update OTA Request API api/v1/update input UpdateRequestDTO
   */

  @RequestMapping(
      path = "/api/v1/update",
      method = RequestMethod.POST,
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public CompletionStage<ResponseEntity<List<InstallationPackageResponseDTO>>> updateOTARequestV(
      final HttpEntity<UpdateRequestDTO> updateRequest) {
    String token = updateRequest.getHeaders().getFirst(Sems1VehicleConstants.AUTH_HEADER_NAME);
    String tenant = updateRequest.getHeaders().getFirst(Sems1VehicleConstants.AUTH_HEADER_TENANT);
    UpdateRequestDTO updateRequestDTO = updateRequest.getBody();
    LOGGER.info(
        Retention.EXTENDED,
        "Entered into UpdateOTARestHandler.updateOTARequestV1: PrimaryId: {}, SecondaryId: {}, Request:{}",
        updateRequestDTO.getPrimaryId(),
        updateRequestDTO.getSecondaryId(),
        updateRequestDTO);
    long startTime = System.currentTimeMillis();
    if (Objects.isNull(token)) {
      LOGGER.info(Retention.EXTENDED, "UpdateOTARestHandler::updateOTARequestV token is null");
      return CompletableFuture.completedFuture(securityGetTokenURLImpl.getOTALoginURL(tenant));
    }
    return semsOTARequestManagerImplBean
        .validateToken(token)
        .thenCompose(
            tok -> {
              if (!tok) {
                return CompletableFuture.completedFuture(
                    securityGetTokenURLImpl.getOTALoginURL(tenant));
              } else {
                return semsOTARequestManagerImplBean
                    .handleOTAInputRequest(updateRequestDTO, startTime, tenant)
                    .thenApply(ResponseEntity::ok);
              }
            });
  }

  /*
   * Update OTA Request API api/v1/update input UpdateRequestDTO
   */

  @RequestMapping(
      path = "/api/v2/update",
      method = RequestMethod.POST,
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public CompletionStage<ResponseEntity<List<InstallationPackageResponseDTO>>> updateOTARequestV2(
      final HttpEntity<UpdateRequestDTO> updateRequest) {
    String token = updateRequest.getHeaders().getFirst(Sems1VehicleConstants.AUTH_HEADER_NAME);
    UpdateRequestDTO updateRequestDTO = updateRequest.getBody();
    LOGGER.info(
        Retention.EXTENDED,
        "Entered into UpdateOTARestHandler.updateOTARequestV2: PrimaryId: {}, SecondaryId: {}, Request:{}",
        updateRequestDTO.getPrimaryId(),
        updateRequestDTO.getSecondaryId(),
        updateRequestDTO);
    long startTime = System.currentTimeMillis();
    if (Objects.isNull(token)) {
      LOGGER.info(Retention.EXTENDED, "UpdateOTARestHandler::updateOTARequestV2 token is null");
      return CompletableFuture.completedFuture(securityGetTokenURLImpl.getOTALoginURL(token));
    }
    return semsOTARequestManagerImplBean
        .validateToken(token)
        .thenCompose(
            tok -> {
              if (!tok) {
                return CompletableFuture.completedFuture(
                    securityGetTokenURLImpl.getOTALoginURL(token));
              } else {
                return semsOTARequestManagerImplBean
                    .handleOTAInputRequestV2(updateRequestDTO, startTime)
                    .thenApply(ResponseEntity::ok);
              }
            });
  }
}
