package com.volvo.tisp.s1vas.rest;

import com.volvo.tisp.framework.logging.Retention;
import com.volvo.tisp.framework.security.annotation.Authentication;
import com.volvo.tisp.s1vas.converter.SemsTokenOutputConverter;
import com.volvo.tisp.s1vas.util.Sems1VehicleConstants;
import com.volvo.tisp.sem1core.client.util.Sem1CoreClientConstants;
import java.nio.charset.StandardCharsets;
import java.util.Objects;
import java.util.concurrent.CompletionStage;
import java.util.concurrent.ExecutionException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Authentication(required = false)
public class LogonRestHandler {
  private static final Logger LOGGER = LoggerFactory.getLogger(LogonRestHandler.class);

  @Autowired SemsTokenOutputConverter semsTokenOutputConverter;
  @Autowired com.volvo.tisp.sem1core.client.LogonClient logonSem1Client;

  @RequestMapping(path = Sem1CoreClientConstants.CONTEXT_PATH_LOGON, method = RequestMethod.POST)
  public CompletionStage<ResponseEntity<String>> logon(final HttpEntity<String> request)
      throws InterruptedException, ExecutionException {
    LOGGER.info(Retention.EXTENDED, "Inside LogonRestHandler::logon");
    String header = request.getHeaders().getFirst(Sems1VehicleConstants.AUTHORIZATION);
    String username = request.getHeaders().getFirst(Sems1VehicleConstants.USERNAME);
    String password = request.getHeaders().getFirst(Sems1VehicleConstants.PASSWORD);

    if (!Objects.nonNull(header)) {
      try {
        String auth = username.concat(":").concat(password);
        final byte[] encodedAuth =
            org.apache.commons.codec.binary.Base64.encodeBase64(
                auth.getBytes(StandardCharsets.US_ASCII));
        header = "Basic " + new String(encodedAuth, StandardCharsets.UTF_8);
      } catch (Exception e) {
        LOGGER.error(
            Retention.EXTENDED,
            "SecurityTokenManagerImpl.login() --> Exception while login with auth :: {} with Exception :{}",
            username,
            e.getMessage());
        return null;
      }
    }

    LOGGER.debug("Entered for sems1-core-server component to hit on LOGON username:{}", username);
    return logonSem1Client
        .logon(header)
        .thenApply(data -> semsTokenOutputConverter.convertResponse(data));
  }
}
