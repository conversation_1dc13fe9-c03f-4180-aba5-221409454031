package com.volvo.tisp.s1vas.util;

import com.amazonaws.HttpMethod;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.GeneratePresignedUrlRequest;
import com.volvo.tisp.framework.logging.Retention;
import com.wirelesscar.config.Config;
import java.net.URL;
import java.util.Date;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class SignedAWSUtils {

  private final Logger LOGGER = LoggerFactory.getLogger(getClass());

  @Autowired private Config config;

  @Autowired() private AmazonS3 amazonS3;

  public static final long EXPIRY_TIME = 1000 * 60 * 60 * 71;

  public SignedAWSUtils() {}

  public URL signFile(String fileName, AmazonS3 s3Client) {

    Date expiration = new Date();

    LOGGER.debug("Generating signed URL.");
    GeneratePresignedUrlRequest generatePresignedUrlRequest = null;
    long expTimeMillis = expiration.getTime() + EXPIRY_TIME;
    expiration.setTime(expTimeMillis);
    generatePresignedUrlRequest =
        new GeneratePresignedUrlRequest(
                config.getString(Sems1VehicleConstants.AWS_BUCKET).get(), fileName)
            .withMethod(HttpMethod.GET)
            .withExpiration(expiration);
    URL url = null;
    try {
      url =
          new URL(
              config.getString(Sems1VehicleConstants.CLOUDFRONT).get()
                  + s3Client.generatePresignedUrl(generatePresignedUrlRequest).getFile());
    } catch (Exception ex) {
      LOGGER.error(
          Retention.EXTENDED,
          "Exception occurred while creating S3-URLS for fileName :{} with exception :{}",
          fileName,
          ex.getMessage());
    }
    return url;
  }
}
