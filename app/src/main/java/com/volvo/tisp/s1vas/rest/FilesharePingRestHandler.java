package com.volvo.tisp.s1vas.rest;

import com.volvo.check.v1.CheckResponse;
import com.volvo.tisp.framework.security.annotation.Authentication;
import com.volvo.tisp.s1vas.converter.CheckResponseOutputConverter;
import com.volvo.tisp.s1vas.domain.DeepPingManagerImpl;
import com.volvo.tisp.s1vas.dto.CheckStatusDTO;
import com.volvo.tisp.s1vas.dto.CheckTypeDTO;
import com.volvo.tisp.s1vas.util.Sems1VehicleConstants;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@Authentication(required = false)
public class FilesharePingRestHandler {
  private static final Logger LOGGER = LoggerFactory.getLogger(FilesharePingRestHandler.class);
  @Autowired DeepPingManagerImpl deepPingManagerImpl;

  @Autowired CheckResponseOutputConverter checkResponseOutputConverter;

  @RequestMapping(
      value = "/v1/ping/shallow/{pong}",
      method = RequestMethod.GET,
      produces = "text/plain;charset=UTF-8")
  public ResponseEntity shallowPing(@PathVariable final String pong) {
    return new ResponseEntity(pong, HttpStatus.OK);
  }

  @GetMapping(
      value = "/v1/ping/deep/fileshare",
      produces = {
        "application/xml",
        "application/json",
        "text/xml",
        "application/vnd.com.wirelesscar.check.v1_0.checkresponse+xml",
        "application/vnd.com.wirelesscar.check.v1_0.checkresponse+json"
      })
  public CompletionStage<CheckResponse> pingFileshare() {
    CheckTypeDTO checkTypeDTO = new CheckTypeDTO();
    checkTypeDTO.setName("Fileshare");

    CheckStatusDTO checkStatusDTO = new CheckStatusDTO();

    try {
      String pingStatus = deepPingManagerImpl.fileShareDeepPing();
      LOGGER.debug("ping status {}", pingStatus);
      if (pingStatus.contains(Sems1VehicleConstants.FILESHARE_PING_SUCCESS)) {
        checkTypeDTO.setFailureReason(null);
        checkTypeDTO.setSuccess(true);
      } else {
        checkTypeDTO.setFailureReason(Sems1VehicleConstants.FILESHARE_PING_FAILURE);
        checkTypeDTO.setSuccess(false);
      }
    } catch (Exception e) {
      LOGGER.error("Exception : {}", e.getMessage());
      checkTypeDTO.setFailureReason("NotFound Exception: " + e.getMessage());
      checkTypeDTO.setSuccess(false);
    }
    checkStatusDTO.getChecks().add(checkTypeDTO);
    checkStatusDTO.setSuccess(checkTypeDTO.isSuccess());

    return CompletableFuture.completedStage(checkResponseOutputConverter.convert(checkStatusDTO));
  }
}
