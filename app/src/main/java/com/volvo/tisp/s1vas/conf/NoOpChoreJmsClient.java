package com.volvo.tisp.s1vas.conf;

import com.volvo.tisp.courier.ChoreJmsClient;
import com.wirelesscar.courier.data.courier._1_1.Chore;
import java.util.concurrent.CompletableFuture;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * No-operation implementation of ChoreJmsClient for when <PERSON><PERSON> is disabled. This allows the
 * application to run without JMS connectivity in local development.
 */
public class NoOpChoreJmsClient implements ChoreJmsClient {

  private static final Logger LOG = LoggerFactory.getLogger(NoOpChoreJmsClient.class);

  @Override
  public CompletableFuture<Integer> send(Chore chore) {
    LOG.debug(
        "J<PERSON> is disabled - NoOp ChoreJmsClient ignoring chore: {}",
        chore != null ? chore.getType() : "null");

    if (chore != null) {
      LOG.info(
          "Would have sent JMS message of type '{}' with workflow ID '{}' (<PERSON><PERSON> disabled)",
          chore.getType(),
          chore.getWorkflowIdentifier());
    }

    // Return a completed future with 0 subscribers to indicate no message was sent
    return CompletableFuture.completedFuture(0);
  }
}
