package com.volvo.tisp.s1vas.domain;

import com.volvo.tisp.s1vas.util.Sems1VehicleConstants;
import com.wirelesscar.config.Config;
import java.io.File;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class DeepPingManagerImpl {
  private static final Logger LOGGER = LoggerFactory.getLogger(DeepPingManagerImpl.class);

  @Autowired private Config config;

  private static final String FILESHARE_PING_SUCCESS = "Looking good : ";
  private static final String FILESHARE_PING_FAILURE =
      "Configured fileshare path is not reachable : ";

  public String fileShareDeepPing() {
    String fileSharePath = config.getString(Sems1VehicleConstants.FILESHARE1_PATH).get();
    try {
      LOGGER.info("!!!! Entered into DeepPingServiceImpl.fileShareDeepPing() ");
      final File dir = new File(fileSharePath);
      LOGGER.info("Fileshare dir{} ", dir.getName());
      if (dir.exists()) {
        LOGGER.info("Directory exists {}", dir.getName());
        return FILESHARE_PING_SUCCESS + fileSharePath;
      } else {
        LOGGER.info("Directory {} doesn't exists ", dir.getName());
      }
    } catch (Exception e) {
      LOGGER.error("Exception :" + e);
    }
    return FILESHARE_PING_FAILURE + fileSharePath;
  }
}
