package com.volvo.tisp.s1vas.util;

import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.S3Object;
import com.volvo.tisp.framework.logging.Retention;
import com.volvo.tisp.s1vas.conf.AppConfig;
import com.volvo.tisp.semsc.client.SemsSecurityClient;
import com.wirelesscar.config.Config;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.concurrent.CompletableFuture;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;

@Component
public class FileShareImpl {

  private final Logger LOGGER = LoggerFactory.getLogger(getClass());

  Config config = new AppConfig().config();
  @Autowired SemsSecurityClient semsSecurityClient;

  @Autowired SignedAWSUtils signedAWSUtils;

  @Autowired private AmazonS3 amazonS3;

  public CompletableFuture<Void> validateGetS3(
      String filename, HttpServletRequest request, HttpServletResponse response) {
    if (Boolean.parseBoolean(config.getString(Sems1VehicleConstants.PRESIGN_DOWNLOAD_S3).get())) {
      return getFilePreSignS3(filename, request, response);
    } else {
      return getFileS3(filename, request, response);
    }
  }

  public CompletableFuture<Boolean> validateToken(String token) {
    LOGGER.info("Inside FileshareRestHandler.validateToken S1VAS started");
    return semsSecurityClient
        .isValidToken(token)
        .thenApply(
            tok -> {
              if (!StringUtils.isBlank(tok) && Boolean.valueOf(tok)) {
                return true;
              } else {
                return false;
              }
            });
  }

  public CompletableFuture<Void> getFilePreSignS3(
      String filename, HttpServletRequest request, HttpServletResponse response) {

    InputStream inputStream = null;
    try {
      URL url = signedAWSUtils.signFile(Sems1VehicleConstants.SEM_ONE_FOLDER + filename, amazonS3);
      HttpURLConnection connection = (HttpURLConnection) url.openConnection();
      connection.setRequestMethod("GET");
      inputStream = connection.getInputStream();

      sendFileToClient(filename, inputStream, connection, null, request, response);
      response.setStatus(200);
    } catch (Exception ex) {
      LOGGER.error(Retention.EXTENDED, "s3 - filename not found {}", ex.getMessage());
      response.setStatus(404);
    }
    return CompletableFuture.completedFuture(null);
  }

  public CompletableFuture<Void> getFileS3(
      String filename, HttpServletRequest request, HttpServletResponse response) {
    HttpHeaders headers = new HttpHeaders();

    S3Object s3object = null;
    InputStream is = null;
    try {
      s3object =
          amazonS3.getObject(
              config.getString(Sems1VehicleConstants.AWS_BUCKET).get(),
              Sems1VehicleConstants.SEM_ONE_FOLDER + filename);
      is = s3object.getObjectContent();
      sendFileToClient(filename, is, null, s3object, request, response);
      response.setStatus(200);
    } catch (Exception ex) {
      LOGGER.error(Retention.EXTENDED, "s3 - filename not found {}", ex.getMessage());
      response.setStatus(404);
    } finally {
      if (s3object != null) {
        try {
          s3object.close();
        } catch (IOException e) {
          LOGGER.error(Retention.EXTENDED, "Unable to close S3 object: {}", e.getMessage());
        }
      }
    }
    return CompletableFuture.completedFuture(null);
  }

  public void sendFileToClient(
      String filename,
      InputStream inputStream,
      HttpURLConnection connection,
      S3Object s3Object,
      HttpServletRequest request,
      HttpServletResponse response) {
    HttpHeaders headers = new HttpHeaders();
    long contentLength;
    try {
      if (Boolean.parseBoolean(config.getString(Sems1VehicleConstants.PRESIGN_DOWNLOAD_S3).get())) {
        contentLength = connection.getContentLength();
      } else {
        contentLength = s3Object.getObjectMetadata().getContentLength();
      }

      String skipBytesStr = request.getHeader(Sems1VehicleConstants.SKIP_BYTES);
      LOGGER.info(Retention.EXTENDED, "s3 - Skipbytes from header: {}", skipBytesStr);
      long skipBytes = 0;
      if (skipBytesStr != null) {
        try {
          skipBytes = Long.parseLong(skipBytesStr);
          LOGGER.info(
              Retention.EXTENDED, "s3 - skipbytes after converting to long : {}", skipBytes);
        } catch (NumberFormatException e) {
          LOGGER.error(
              Retention.EXTENDED, "s3 - exception while converting bytes to long : {}", skipBytes);
          skipBytes = 0;
        }
      }

      LOGGER.info(
          Retention.EXTENDED,
          "s3 - bytes before setting the response " + (int) (contentLength - skipBytes));
      // TECH-160702 Skipping bytes during pause and resume
      response.setContentLength((int) (contentLength - skipBytes));
      response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
      response.setHeader(
          Sems1VehicleConstants.CONTENT_DISPOSITION,
          Sems1VehicleConstants.ATTACHMENT_FILENAME + filename + "\"");
      headers.add(Sems1VehicleConstants.CONTENTTYPE, Sems1VehicleConstants.BINARY_OCTATE);
      // copy it to response's OutputStream
      OutputStream outputStream = response.getOutputStream();
      try (BufferedInputStream bis = new BufferedInputStream(inputStream);
          BufferedOutputStream bos = new BufferedOutputStream(outputStream)) {
        IOUtils.copyLarge(bis, bos, skipBytes, contentLength - skipBytes);
      } catch (IOException ioException) {
        LOGGER.error(
            Retention.EXTENDED,
            "error while converting to buffered outputStream {} ",
            ioException.getMessage());
      } finally {
        outputStream.close();
        inputStream.close();
        response.flushBuffer();
      }
      LOGGER.info(Retention.EXTENDED, "s3 - /v1/fileshare/{} found", filename);
    } catch (Exception ex) {
      LOGGER.error(Retention.EXTENDED, "s3 - filename not found {}", ex.getMessage());
    }
  }
}
