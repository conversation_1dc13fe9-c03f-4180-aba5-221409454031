package com.volvo.tisp.s1vas.domain;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.volvo.tisp.s1vas.util.Sems1MessageConstants;
import com.volvo.tisp.sem1core.client.OTARequestClient;
import com.volvo.tisp.sem1core.client.dto.UpdateResultDTO;
import com.volvo.tisp.sem1core.client.dto.VehicleInstallationPackageDTO;
import com.volvo.tisp.semsc.client.SemsSecurityClient;
import com.volvo.tisp.semslib.exceptions.BadRequestException;
import com.volvo.tisp.semslib.exceptions.InternalServerErrorException;
import com.volvo.tisp.semslib.exceptions.InvalidCredentialsException;
import com.volvo.tisp.semslib.exceptions.NotFoundException;
import com.wirelesscar.config.Config;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.concurrent.ExecutionException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClientResponseException;

@Component
public class SemsResultManagerImplBean {

  private final Logger LOGGER = LoggerFactory.getLogger(getClass());

  @Autowired private OTARequestClient otaSem1Client;

  @Autowired SemsSecurityClient semsSecurityClient;

  @Autowired Config config;

  private final ObjectMapper mapper = new ObjectMapper();

  public CompletableFuture<List<VehicleInstallationPackageDTO>> handleOTAResultInputRequest(
      UpdateResultDTO updateResultDTO, long startTime) {

    return updateResult(updateResultDTO, startTime)
        .exceptionally(
            throwable -> {
              if (throwable.getCause() instanceof InvalidCredentialsException
                  || throwable.getCause() instanceof WebClientResponseException.Unauthorized) {
                LOGGER.error("Token is not valid:");
                throw new InvalidCredentialsException(
                    Sems1MessageConstants.TOKEN_NOT_VALID_MESSAGE);
              } else if (throwable.getCause() instanceof NotFoundException
                  || throwable.getCause() instanceof WebClientResponseException.NotFound) {
                LOGGER.error(
                    "The data for chassisid {} is not found", updateResultDTO.getPrimaryId());
                throw new NotFoundException(
                    "The vehicle data for the chassis id:" + updateResultDTO.getPrimaryId());
              } else if (throwable.getCause() instanceof IllegalArgumentException) {
                throw new BadRequestException("Illegal arguments");
              } else if (throwable.getCause() instanceof InterruptedException
                  || throwable.getCause() instanceof ExecutionException) {
                throw new InvalidCredentialsException("Token conversion exception");
              } else {
                throw new InternalServerErrorException("An error occurred while UpdateResult");
              }
            });
  }

  public CompletableFuture<Boolean> validateToken(String token) {
    LOGGER.info(
        "Inside SemsOTARequestManagerImplBean S1VAS method TokenValidator.validateToken() started");
    return semsSecurityClient
        .isValidToken(token)
        .thenApply(
            tok -> {
              if (!StringUtils.isBlank(tok) && Boolean.parseBoolean(tok)) {
                return true;
              } else {
                return false;
              }
            });
  }

  private CompletableFuture<List<VehicleInstallationPackageDTO>> updateResult(
      UpdateResultDTO updateResultDTO, long startTime) {

    LOGGER.debug(
        "Entered into Sems1-core-server core with UpdateResultRequest:{}", updateResultDTO);
    return otaSem1Client
        .updateResultSem1(convertToSem1(updateResultDTO))
        .thenCompose(resp -> printLogssem1(resp, startTime, updateResultDTO.getPrimaryId()));
  }

  private com.volvo.tisp.sem1core.client.dto.UpdateResultDTO convertToSem1(
      UpdateResultDTO updateResultDTO) {
    com.volvo.tisp.sem1core.client.dto.UpdateResultDTO updateResultDTOSem1 =
        new com.volvo.tisp.sem1core.client.dto.UpdateResultDTO();
    try {
      updateResultDTOSem1 =
          mapper.convertValue(
              updateResultDTO,
              new TypeReference<com.volvo.tisp.sem1core.client.dto.UpdateResultDTO>() {});
    } catch (IllegalArgumentException exep) {
      LOGGER.error("unable to convert to response object {} ", updateResultDTO);
      throw new IllegalArgumentException("error while mapping to pojo object ");
    }
    return updateResultDTOSem1;
  }

  private CompletionStage<List<VehicleInstallationPackageDTO>> printLogssem1(
      List<com.volvo.tisp.sem1core.client.dto.VehicleInstallationPackageDTO> resp,
      long startTime,
      String primaryId) {
    List<VehicleInstallationPackageDTO> response;
    LOGGER.info(
        "/v1/update sems1-core-server for Vehicle :{} time taken to processRequest:{} SemsOTARequestManagerImplBean.updateResponse:{}",
        primaryId,
        (System.currentTimeMillis() - startTime),
        resp);
    try {
      response = mapper.convertValue(resp, new TypeReference<>() {});
    } catch (IllegalArgumentException exep) {
      LOGGER.error("unable to convert to response object {} ", resp);
      throw new IllegalArgumentException("error while mapping to pojo object ");
    }
    return CompletableFuture.completedFuture(response);
  }
}
