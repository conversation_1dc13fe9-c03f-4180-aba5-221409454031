package com.volvo.tisp.s1vas.domain;

import com.volvo.tisp.s1vas.util.Sems1VehicleConstants;
import com.volvo.tisp.sem1core.client.dto.InstallationPackageResponseDTO;
import com.volvo.tisp.sem1core.client.dto.VehicleInstallationPackageDTO;
import com.wirelesscar.config.Config;
import java.util.ArrayList;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

@Component
public class SecurityGetTokenURLImpl {

  private static final Logger LOGGER = LoggerFactory.getLogger(SecurityGetTokenURLImpl.class);
  @Autowired private Config config;

  public ResponseEntity<List<VehicleInstallationPackageDTO>> getResultLoginURL(String tenant) {
    LOGGER.info("Entered inside the SecurityGetTokenURLImpl.getResultLoginURL token URL");
    HttpHeaders httpHeaders = new HttpHeaders();
    httpHeaders.set(Sems1VehicleConstants.AUTHENTICATE, Sems1VehicleConstants.X_AUTH_TOKEN_REALM);
    httpHeaders.setContentLength(0);
    if (Sems1VehicleConstants.NA_TENANT.equalsIgnoreCase(tenant)) {
      httpHeaders.set(
          Sems1VehicleConstants.AUTH_HEADER_URL,
          config.getString(Sems1VehicleConstants.NA_TOKEN_GENERATE_URL).get());
    } else {
      httpHeaders.set(
          Sems1VehicleConstants.AUTH_HEADER_URL,
          config.getString(Sems1VehicleConstants.TOKEN_GENERATE_URL).get());
    }
    httpHeaders.setContentType(MediaType.APPLICATION_JSON);
    List<HttpMethod> allowedMethods = new ArrayList<>();
    allowedMethods.add(HttpMethod.GET);
    allowedMethods.add(HttpMethod.POST);
    allowedMethods.add(HttpMethod.PUT);
    allowedMethods.add(HttpMethod.DELETE);
    httpHeaders.setAccessControlAllowMethods(allowedMethods);
    List<String> allowHeaders = new ArrayList<>();
    allowHeaders.add(Sems1VehicleConstants.AUTH_HEADER_NAME);
    allowHeaders.add(Sems1VehicleConstants.AUTHORIZATION);
    allowHeaders.add(Sems1VehicleConstants.CONTENT_TYPE);
    httpHeaders.setAccessControlAllowHeaders(allowHeaders);
    List<String> exposeHeaders = new ArrayList<>();
    exposeHeaders.add(Sems1VehicleConstants.AUTH_HEADER_NAME);
    httpHeaders.setAccessControlExposeHeaders(exposeHeaders);
    httpHeaders.setAccessControlAllowCredentials(true);
    return new ResponseEntity<>(null, httpHeaders, HttpStatus.UNAUTHORIZED);
  }

  public ResponseEntity<List<InstallationPackageResponseDTO>> getOTALoginURL(String tenant) {
    LOGGER.info("Entered inside the SecurityGetTokenURLImpl.getOTALoginURL token URL");
    HttpHeaders httpHeaders = new HttpHeaders();
    httpHeaders.set(Sems1VehicleConstants.AUTHENTICATE, Sems1VehicleConstants.X_AUTH_TOKEN_REALM);
    httpHeaders.setContentLength(0);
    if (Sems1VehicleConstants.NA_TENANT.equalsIgnoreCase(tenant)) {
      httpHeaders.set(
          Sems1VehicleConstants.AUTH_HEADER_URL,
          config.getString(Sems1VehicleConstants.NA_TOKEN_GENERATE_URL).get());
    } else {
      httpHeaders.set(
          Sems1VehicleConstants.AUTH_HEADER_URL,
          config.getString(Sems1VehicleConstants.TOKEN_GENERATE_URL).get());
    }
    httpHeaders.setContentType(MediaType.APPLICATION_JSON);
    List<HttpMethod> allowedMethods = new ArrayList<>();
    allowedMethods.add(HttpMethod.GET);
    allowedMethods.add(HttpMethod.POST);
    allowedMethods.add(HttpMethod.PUT);
    allowedMethods.add(HttpMethod.DELETE);
    httpHeaders.setAccessControlAllowMethods(allowedMethods);
    List<String> allowHeaders = new ArrayList<>();
    allowHeaders.add(Sems1VehicleConstants.AUTH_HEADER_NAME);
    allowHeaders.add(Sems1VehicleConstants.AUTHORIZATION);
    allowHeaders.add(Sems1VehicleConstants.CONTENT_TYPE);
    httpHeaders.setAccessControlAllowHeaders(allowHeaders);
    List<String> exposeHeaders = new ArrayList<>();
    exposeHeaders.add(Sems1VehicleConstants.AUTH_HEADER_NAME);
    httpHeaders.setAccessControlExposeHeaders(exposeHeaders);
    httpHeaders.setAccessControlAllowCredentials(true);
    return new ResponseEntity<>(null, httpHeaders, HttpStatus.UNAUTHORIZED);
  }

  public ResponseEntity<Void> getFileLoginURL(String token) {
    LOGGER.info("Entered inside the SecurityGetTokenURLImpl.getFileLoginURL token URL");
    HttpHeaders httpHeaders = new HttpHeaders();
    httpHeaders.set(Sems1VehicleConstants.AUTHENTICATE, Sems1VehicleConstants.X_AUTH_TOKEN_REALM);
    httpHeaders.setContentLength(0);
    httpHeaders.set(
        Sems1VehicleConstants.AUTH_HEADER_URL,
        config.getString(Sems1VehicleConstants.TOKEN_GENERATE_URL).get());
    httpHeaders.setContentType(MediaType.APPLICATION_JSON);
    List<HttpMethod> allowedMethods = new ArrayList<>();
    allowedMethods.add(HttpMethod.GET);
    allowedMethods.add(HttpMethod.POST);
    allowedMethods.add(HttpMethod.PUT);
    allowedMethods.add(HttpMethod.DELETE);
    httpHeaders.setAccessControlAllowMethods(allowedMethods);
    List<String> allowHeaders = new ArrayList<>();
    allowHeaders.add(Sems1VehicleConstants.AUTH_HEADER_NAME);
    allowHeaders.add(Sems1VehicleConstants.AUTHORIZATION);
    allowHeaders.add(Sems1VehicleConstants.CONTENT_TYPE);
    httpHeaders.setAccessControlAllowHeaders(allowHeaders);
    List<String> exposeHeaders = new ArrayList<>();
    exposeHeaders.add(Sems1VehicleConstants.AUTH_HEADER_NAME);
    httpHeaders.setAccessControlExposeHeaders(exposeHeaders);
    httpHeaders.setAccessControlAllowCredentials(true);
    return new ResponseEntity<>(null, httpHeaders, HttpStatus.UNAUTHORIZED);
  }
}
