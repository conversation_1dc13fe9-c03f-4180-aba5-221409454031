package com.volvo.tisp.s1vas.converter;

import com.volvo.check.v1.Application;
import com.volvo.check.v1.CheckResponse;
import com.volvo.check.v1.CheckType;
import com.volvo.tisp.s1vas.dto.CheckStatusDTO;
import com.volvo.tisp.s1vas.dto.CheckTypeDTO;
import com.wirelesscar.config.Config;
import com.wirelesscar.config.ConfigFactory;
import java.math.BigInteger;
import java.util.Collection;
import org.springframework.stereotype.Component;

@Component
public class CheckResponseOutputConverter {
  private Config config = ConfigFactory.getConfig();

  public CheckResponse convert(CheckStatusDTO checkStatusDto) {
    Application application = new Application();
    application.setLongName(config.getComponentLongName());
    application.setShortName(config.getComponentShortName());
    application.setComponentVersion(config.getComponentVersion());
    application.getChecks().addAll(convertChecks(checkStatusDto));
    application.setSuccess(checkStatusDto.isSuccess());
    CheckResponse checkResponse = new CheckResponse();
    checkResponse.setApplication(application);
    checkResponse.setMinorVersion(BigInteger.valueOf(0));
    return checkResponse;
  }

  private Collection<? extends CheckType> convertChecks(CheckStatusDTO checkStatusDto) {
    return checkStatusDto.getChecks().stream().map(this::convertCheck).toList();
  }

  private CheckType convertCheck(CheckTypeDTO checkTypeDto) {
    CheckType checkType = new CheckType();
    checkType.setName(checkTypeDto.getName());
    checkType.setSuccess(checkTypeDto.isSuccess());
    checkType.setFailureReason(checkTypeDto.getFailureReason());
    return checkType;
  }
}
