package com.volvo.tisp.s1vas.rest;

import com.volvo.tisp.framework.logging.Retention;
import com.volvo.tisp.framework.security.annotation.Authentication;
import com.volvo.tisp.s1vas.domain.SecurityGetTokenURLImpl;
import com.volvo.tisp.s1vas.domain.SemsResultManagerImplBean;
import com.volvo.tisp.s1vas.util.Sems1VehicleConstants;
import com.volvo.tisp.sem1core.client.dto.UpdateResultDTO;
import com.volvo.tisp.sem1core.client.dto.VehicleInstallationPackageDTO;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Authentication(required = false)
public class UpdateResultRestHandler {

  private final Logger LOGGER = LoggerFactory.getLogger(getClass());

  @Autowired SemsResultManagerImplBean semsResultManagerImplBean;
  @Autowired SecurityGetTokenURLImpl securityGetTokenURLImpl;

  /*
   * Update OTA Result Request API api/v1/updateResult input UpdateResultDTO
   */

  @RequestMapping(
      path = "/api/v1/updateResult",
      method = RequestMethod.POST,
      consumes = {"application/json", "application/json"},
      produces = {"application/json", "application/json"})
  public CompletionStage<ResponseEntity<List<VehicleInstallationPackageDTO>>> updateResultRequestV1(
      final HttpEntity<UpdateResultDTO> updateResult) {
    String token = updateResult.getHeaders().getFirst(Sems1VehicleConstants.AUTH_HEADER_NAME);
    String tenant = updateResult.getHeaders().getFirst(Sems1VehicleConstants.AUTH_HEADER_TENANT);
    UpdateResultDTO updateResultDTO = updateResult.getBody();
    LOGGER.info(
        Retention.EXTENDED,
        "Entered into UpdateResultRestHandler.updateResultRequestV1: {}",
        updateResult.getBody());
    long startTime = System.currentTimeMillis();
    if (Objects.isNull(token)) {
      return CompletableFuture.completedFuture(securityGetTokenURLImpl.getResultLoginURL(tenant));
    }
    return semsResultManagerImplBean
        .validateToken(token)
        .thenCompose(
            tok -> {
              if (!tok) {
                return CompletableFuture.completedFuture(
                    securityGetTokenURLImpl.getResultLoginURL(tenant));
              } else {
                return semsResultManagerImplBean
                    .handleOTAResultInputRequest(updateResultDTO, startTime)
                    .thenApply(ResponseEntity::ok);
              }
            });
  }
}
