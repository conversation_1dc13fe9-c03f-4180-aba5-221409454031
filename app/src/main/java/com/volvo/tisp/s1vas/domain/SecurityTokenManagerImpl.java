package com.volvo.tisp.s1vas.domain;

import com.volvo.tisp.semsc.client.SemsSecurityClient;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.CompletableFuture;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class SecurityTokenManagerImpl {

  private static final Logger LOGGER = LoggerFactory.getLogger(SecurityTokenManagerImpl.class);

  @Autowired SemsSecurityClient semsSecurityClient;

  public CompletableFuture<String> loginByusernamePass(String auth) {
    LOGGER.info("Inside method SecurityTokenManagerImpl.loginByusernamePass()");
    long startTime = System.currentTimeMillis();
    try {
      final byte[] encodedAuth =
          org.apache.commons.codec.binary.Base64.encodeBase64(
              auth.getBytes(StandardCharsets.US_ASCII));
      final String authHeader = "Basic " + new String(encodedAuth, StandardCharsets.UTF_8);
      return semsSecurityClient
          .getTokenByHeader(authHeader)
          .thenApply(
              (String token) -> {
                LOGGER.info(
                    "Token generated is with time:{}", (System.currentTimeMillis() - startTime));
                return token;
              })
          .exceptionally(
              e -> {
                LOGGER.error("Error occurred generating token : {}", e);
                return null;
              });
    } catch (Exception e) {
      LOGGER.error(
          "SecurityTokenManagerImpl.login() --> Exception while login with auth :: "
              + auth
              + " : "
              + e.getMessage());
      return null;
    }
  }

  public CompletableFuture<String> login(String authHeader) {
    LOGGER.info("Inside method SecurityTokenManagerImpl.login()");
    long startTime = System.currentTimeMillis();
    return semsSecurityClient
        .getTokenByHeader(authHeader)
        .thenApply(
            (String token) -> {
              LOGGER.info(
                  "Token generated is with time:{}", (System.currentTimeMillis() - startTime));
              return token;
            })
        .exceptionally(
            e -> {
              LOGGER.error("Error occurred generating token : {}", e.getMessage());
              return null;
            });
  }
}
