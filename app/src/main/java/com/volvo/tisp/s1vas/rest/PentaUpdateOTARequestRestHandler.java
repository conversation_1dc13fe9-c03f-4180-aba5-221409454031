package com.volvo.tisp.s1vas.rest;

import com.volvo.tisp.framework.logging.Retention;
import com.volvo.tisp.framework.security.annotation.Authentication;
import com.volvo.tisp.s1vas.domain.SecurityGetTokenURLImpl;
import com.volvo.tisp.s1vas.domain.SemsOTARequestManagerImplBean;
import com.volvo.tisp.s1vas.util.Sems1VehicleConstants;
import com.volvo.tisp.sem1core.client.dto.InstallationPackageResponseDTO;
import com.volvo.tisp.sem1core.client.dto.PentaUpdateRequestDTO;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Authentication(required = false)
public class PentaUpdateOTARequestRestHandler {

  private final Logger LOGGER = LoggerFactory.getLogger(getClass());

  @Autowired SemsOTARequestManagerImplBean semsOTARequestManagerImplBean;

  @Autowired SecurityGetTokenURLImpl securityGetTokenURLImpl;

  /*
   * Update OTA Request API api/v1/update input UpdateRequestDTO
   */

  @RequestMapping(
      path = "/penta/api/v1/update",
      method = RequestMethod.POST,
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public CompletionStage<ResponseEntity<List<InstallationPackageResponseDTO>>> updateOTARequestV1(
      final HttpEntity<PentaUpdateRequestDTO> pentaUpdateRequest) {
    String token = pentaUpdateRequest.getHeaders().getFirst(Sems1VehicleConstants.AUTH_HEADER_NAME);
    PentaUpdateRequestDTO pentaUpdateRequestDTO = pentaUpdateRequest.getBody();
    LOGGER.info(
        Retention.EXTENDED,
        "Entered into PentaUpdateOTARequestRestHandler.updateOTARequestV1: PrimaryId: {}, SecondaryId: {}, Request:{}",
        pentaUpdateRequestDTO.getPrimaryId(),
        pentaUpdateRequestDTO.getSecondaryId(),
        pentaUpdateRequestDTO);
    long startTime = System.currentTimeMillis();
    if (Objects.isNull(token)) {
      LOGGER.info(Retention.EXTENDED, "UpdateOTARestHandler::updateOTARequestV1 token is null");
      return CompletableFuture.completedFuture(securityGetTokenURLImpl.getOTALoginURL(token));
    }
    return semsOTARequestManagerImplBean
        .validateToken(token)
        .thenCompose(
            tok -> {
              if (!tok) {
                return CompletableFuture.completedFuture(
                    securityGetTokenURLImpl.getOTALoginURL(token));
              } else {
                return semsOTARequestManagerImplBean
                    .handlePentaOTAInputRequestV1(pentaUpdateRequestDTO, startTime)
                    .thenApply(ResponseEntity::ok);
              }
            });
  }
}
