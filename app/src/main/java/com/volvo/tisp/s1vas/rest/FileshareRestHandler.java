package com.volvo.tisp.s1vas.rest;

import com.volvo.tisp.framework.logging.Retention;
import com.volvo.tisp.framework.security.annotation.Authentication;
import com.volvo.tisp.s1vas.domain.SecurityGetTokenURLImpl;
import com.volvo.tisp.s1vas.util.FileShareImpl;
import com.volvo.tisp.s1vas.util.Sems1VehicleConstants;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@Authentication(required = false)
@RestController
public class FileshareRestHandler {

  private final Logger LOGGER = LoggerFactory.getLogger(getClass());
  @Autowired FileShareImpl fileShareImpl;
  @Autowired SecurityGetTokenURLImpl securityGetTokenURLImpl;

  @RequestMapping(
      path = "/fileshare/v1/fileshare/{filename:.*}",
      method = RequestMethod.GET,
      consumes = {"application/json", "application/octet-stream"},
      produces = {"application/json", "application/octet-stream"})
  public CompletionStage<ResponseEntity<Void>> fileShare(
      @PathVariable(Sems1VehicleConstants.FILENAME) String filename,
      HttpServletRequest request,
      HttpServletResponse response) {
    LOGGER.info(Retention.EXTENDED, "Entered into FileshareRestHandler.fileShare: {}", filename);

    String token1 = request.getHeader(Sems1VehicleConstants.AUTH_HEADER_NAME);
    String token = request.getParameter(Sems1VehicleConstants.AUTH_HEADER_NAME);
    if (Objects.isNull(token) && Objects.isNull(token1)) {
      return CompletableFuture.completedFuture(securityGetTokenURLImpl.getFileLoginURL(token));
    } else if (Objects.nonNull(token1)) {
      return fileShareImpl
          .validateToken(token1)
          .thenCompose(
              tok -> {
                if (!tok) {
                  return CompletableFuture.completedFuture(
                      securityGetTokenURLImpl.getFileLoginURL(token1));
                } else if (Objects.nonNull(filename)) { // need to add boolean flag for s3
                  return fileShareImpl
                      .validateGetS3(filename, request, response)
                      .thenApply(ResponseEntity::ok);
                } else {
                  return null;
                }
              });
    } else {
      return fileShareImpl
          .validateToken(token)
          .thenCompose(
              tok -> {
                if (!tok) {
                  return CompletableFuture.completedFuture(
                      securityGetTokenURLImpl.getFileLoginURL(token));
                } else if (Objects.nonNull(filename)) {
                  return fileShareImpl
                      .validateGetS3(filename, request, response)
                      .thenApply(ResponseEntity::ok);
                } else {
                  return null;
                }
              });
    }
  }
}
