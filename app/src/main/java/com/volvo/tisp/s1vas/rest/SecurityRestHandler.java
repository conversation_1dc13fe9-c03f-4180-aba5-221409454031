package com.volvo.tisp.s1vas.rest;

import com.volvo.tisp.framework.security.annotation.Authentication;
import com.volvo.tisp.s1vas.converter.SemsTokenOutputConverter;
import com.volvo.tisp.s1vas.domain.SecurityTokenManagerImpl;
import com.volvo.tisp.s1vas.util.Sems1VehicleConstants;
import com.volvo.tisp.semslib.exceptions.InvalidCredentialsException;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Authentication(required = false)
public class SecurityRestHandler {

  private final Logger LOGGER = LoggerFactory.getLogger(getClass());

  @Autowired SecurityTokenManagerImpl securityTokenManagerImpl;

  @Autowired SemsTokenOutputConverter semsTokenOutputConverter;

  /**
   * getting token
   *
   * @param request
   * @return
   */
  @RequestMapping(
      path = "/security/v1/api/login",
      method = RequestMethod.GET,
      produces = {"application/xml", "application/json"})
  public CompletionStage<ResponseEntity<String>> getToken(final HttpEntity<String> request) {
    String header = request.getHeaders().getFirst(Sems1VehicleConstants.AUTHORIZATION);
    String username = request.getHeaders().getFirst(Sems1VehicleConstants.USERNAME);
    String password = request.getHeaders().getFirst(Sems1VehicleConstants.PASSWORD);
    if (Objects.nonNull(header)) {
      return securityTokenManagerImpl
          .login(header)
          .thenApply(data -> semsTokenOutputConverter.convertResponse(data));
    } else if (Objects.nonNull(username) && Objects.nonNull(password)) {
      String usernamePass = username.concat(":").concat(password);
      return securityTokenManagerImpl
          .loginByusernamePass(usernamePass)
          .thenApply(data -> semsTokenOutputConverter.convertResponse(data));
    } else {
      LOGGER.error("Exception occurred during getToken for Username: {}", username);
      return CompletableFuture.completedStage(new ResponseEntity<>(HttpStatus.UNAUTHORIZED));
    }
  }

  @RequestMapping(
      path = "/security/v1/api/login",
      method = RequestMethod.POST,
      produces = {"application/xml", "application/json"})
  public CompletionStage<ResponseEntity<String>> postToken(final HttpEntity<String> request) {
    String header = request.getHeaders().getFirst(Sems1VehicleConstants.AUTHORIZATION);
    String username = request.getHeaders().getFirst(Sems1VehicleConstants.USERNAME);
    String password = request.getHeaders().getFirst(Sems1VehicleConstants.PASSWORD);
    if (Objects.nonNull(header)) {
      return securityTokenManagerImpl
          .login(header)
          .thenApply(data -> semsTokenOutputConverter.convertResponse(data));
    } else if (Objects.nonNull(username) && Objects.nonNull(password)) {
      String usernamePass = username.concat(":").concat(password);
      return securityTokenManagerImpl
          .loginByusernamePass(usernamePass)
          .thenApply(data -> semsTokenOutputConverter.convertResponse(data));
    } else {
      LOGGER.error("Exception occurred during getToken for Username: {}", username);
      throw new InvalidCredentialsException("Input Authentication is null");
    }
  }
}
