package com.volvo.tisp.s1vas.conf;

import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;

import com.volvo.tisp.sem1core.client.LogonClient;
import com.volvo.tisp.sem1core.client.OTARequestClient;
import com.volvo.tisp.sem1core.client.PentaOTARequestClient;
import com.volvo.tisp.sem1core.client.VehicleClient;
import com.volvo.tisp.semsc.client.SemsSecurityClient;
import com.volvo.tisp.semslib.utils.SemExceptionHandler;

import com.volvo.tisp.suc.client.RequestDataThresholdClient;
import com.wirelesscar.config.Config;
import com.wirelesscar.config.ConfigFactory;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import org.springframework.web.reactive.function.client.WebClient;

@Configuration
public class AppConfig {

  @Bean(name = "config")
  public Config config() {
    return ConfigFactory.getConfig();
  }

  @Bean(name = "amazonS3")
  @ConditionalOnProperty(name = "aws.s3.enabled", havingValue = "true", matchIfMissing = false)
  public AmazonS3 amazonS3() {

    BasicAWSCredentials basicAWSCredentials =
        new BasicAWSCredentials(
            ConfigFactory.getConfig().getString("accessKeyId").get(),
            ConfigFactory.getConfig().getString("secretAccessKey").get());
    return AmazonS3ClientBuilder.standard()
        .withCredentials(new AWSStaticCredentialsProvider(basicAWSCredentials))
        .withRegion("eu-west-1")
        .build();
  }

  @Bean
  OTARequestClient otaSem1Client(WebClient.Builder builder) {
    return new OTARequestClient(builder);
  }

  @Bean
  LogonClient logonSem1Client(WebClient.Builder builder) {
    return new LogonClient(builder);
  }

  @Bean
  SemsSecurityClient semsSecurityClient(WebClient.Builder builder) {
    return new SemsSecurityClient(builder);
  }

  @Bean
  VehicleClient vehicleClient(WebClient.Builder builder) {
    return new VehicleClient(builder);
  }

  @Bean
  RequestDataThresholdClient requestDataThresholdClient(WebClient.Builder builder) {
    return new RequestDataThresholdClient(builder);
  }

  @Bean
  SemExceptionHandler semsExceptionHandler() {
    return new SemExceptionHandler();
  }







  @Bean
  PentaOTARequestClient pentaOTARequestClient(WebClient.Builder builder) {
    return new PentaOTARequestClient(builder);
  }


}
