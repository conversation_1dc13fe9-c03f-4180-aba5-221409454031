package com.volvo.tisp.s1vas.rest;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;

import com.volvo.tisp.s1vas.domain.SecurityGetTokenURLImpl;
import com.volvo.tisp.s1vas.domain.SemsResultManagerImplBean;
import com.volvo.tisp.s1vas.util.Sems1VehicleTestConstants;
import com.volvo.tisp.s1vas.util.TestUtil;
import com.volvo.tisp.sem1core.client.dto.VehicleInstallationPackageDTO;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.http.ResponseEntity;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class PentaUpdateResultRestHandlerTest {

  @InjectMocks PentaUpdateResultRestHandler pentaUpdateResultRestHandler;
  @Mock SemsResultManagerImplBean semsResultManagerImplBean;
  @Mock SecurityGetTokenURLImpl securityGetTokenURLImpl;

  PentaUpdateResultRestHandlerTest() throws IOException {}

  String updateResultRequest =
      TestUtil.readingJsonRequestFromResource(
          Sems1VehicleTestConstants.FILE_ROOT_PATH.concat(
              Sems1VehicleTestConstants.PENTA_UPDATERESULT_REQUEST));

  @Test
  void testUpdatePentaResultV1() throws IOException {
    List<VehicleInstallationPackageDTO> vehicleInstallationPackageList = new ArrayList<>();
    Mockito.when(semsResultManagerImplBean.validateToken(any()))
        .thenReturn(CompletableFuture.completedFuture(true));
    Mockito.when(semsResultManagerImplBean.handleOTAResultInputRequest(any(), anyLong()))
        .thenReturn(CompletableFuture.completedFuture(vehicleInstallationPackageList));
    CompletionStage<ResponseEntity<List<VehicleInstallationPackageDTO>>> response =
        pentaUpdateResultRestHandler.updatePentaResultV1(
            TestUtil.getUpdateResultRequestWithHeaders(updateResultRequest));
    assertNotNull(response);
  }

  @Test
  void testUpdatePentaResultV1WithValidToken() throws IOException {
    List<VehicleInstallationPackageDTO> vehicleInstallationPackageList = new ArrayList<>();
    Mockito.when(semsResultManagerImplBean.validateToken(any()))
        .thenReturn(CompletableFuture.completedFuture(false));
    Mockito.when(securityGetTokenURLImpl.getResultLoginURL(any()))
        .thenReturn(ResponseEntity.of(Optional.of(vehicleInstallationPackageList)));
    CompletionStage<ResponseEntity<List<VehicleInstallationPackageDTO>>> response =
        pentaUpdateResultRestHandler.updatePentaResultV1(
            TestUtil.getUpdateResultRequestWithHeaders(updateResultRequest));
    assertNotNull(response);
  }

  @Test
  void testUpdatePentaResultV1WithNullToken() throws IOException {
    List<VehicleInstallationPackageDTO> vehicleInstallationPackageList = new ArrayList<>();
    Mockito.when(securityGetTokenURLImpl.getResultLoginURL(any()))
        .thenReturn(ResponseEntity.of(Optional.of(vehicleInstallationPackageList)));
    CompletionStage<ResponseEntity<List<VehicleInstallationPackageDTO>>> response =
        pentaUpdateResultRestHandler.updatePentaResultV1(
            TestUtil.getUpdateResultRequestWithHeadersWithNullToken(updateResultRequest));
    assertNotNull(response);
  }
}
