package com.volvo.tisp.s1vas.converter;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.when;

import com.volvo.check.v1.CheckResponse;
import com.volvo.tisp.s1vas.dto.CheckStatusDTO;
import com.volvo.tisp.s1vas.util.Sems1VehicleTestConstants;
import com.wirelesscar.config.Config;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class CheckResponseOutputConverterTest {

  @InjectMocks CheckResponseOutputConverter checkResponseOutputConverter;
  @Mock Config config;

  @Test
  void testConvert() {
    when(config.getComponentLongName()).thenReturn(Sems1VehicleTestConstants.COMPONENT_LONG_NAME);
    when(config.getComponentShortName()).thenReturn(Sems1VehicleTestConstants.COMPONENT_SHORT_NAME);
    when(config.getComponentVersion()).thenReturn(Sems1VehicleTestConstants.COMPONENT_VERSION);
    CheckStatusDTO checkStatusDTO = new CheckStatusDTO();
    checkStatusDTO.setSuccess(true);
    checkStatusDTO.setFailureReason(Sems1VehicleTestConstants.FAILURE_REASON);
    CheckResponse response = checkResponseOutputConverter.convert(checkStatusDTO);
    assertNotNull(response);
  }
}
