package com.volvo.tisp.s1vas.util;

import static com.volvo.tisp.s1vas.util.Sems1VehicleTestConstants.HTTP_BODY;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.volvo.tisp.sem1core.client.dto.PentaUpdateRequestDTO;
import com.volvo.tisp.sem1core.client.dto.UpdateRequestDTO;
import com.volvo.tisp.sem1core.client.dto.UpdateResultDTO;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.charset.Charset;
import org.apache.commons.io.IOUtils;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;

public class TestUtil {

  public static HttpEntity<String> getRequestWithHeader() {
    HttpHeaders headers = new HttpHeaders();
    headers.set(
        Sems1VehicleTestConstants.AUTHORIZATION, Sems1VehicleTestConstants.AUTHORIZATION_VALUE);
    headers.set(Sems1VehicleTestConstants.USERNAME, Sems1VehicleTestConstants.USER);
    headers.set(Sems1VehicleTestConstants.PASSWORD, Sems1VehicleTestConstants.USER);
    return new HttpEntity<>(HTTP_BODY, headers);
  }

  public static HttpEntity<String> getRequestWithNullAuthorizationValue() {
    HttpHeaders headers = new HttpHeaders();
    headers.set(Sems1VehicleTestConstants.AUTHORIZATION, null);
    headers.set(Sems1VehicleTestConstants.USERNAME, Sems1VehicleTestConstants.USER);
    headers.set(Sems1VehicleTestConstants.PASSWORD, Sems1VehicleTestConstants.USER);
    return new HttpEntity<>(HTTP_BODY, headers);
  }

  public static HttpEntity<String> getRequestWithoutPasswordInHeaders() {
    HttpHeaders headers = new HttpHeaders();
    headers.set(Sems1VehicleTestConstants.AUTHORIZATION, null);
    headers.set(Sems1VehicleTestConstants.USERNAME, Sems1VehicleTestConstants.USER);
    headers.set(Sems1VehicleTestConstants.PASSWORD, null);
    return new HttpEntity<>(HTTP_BODY, headers);
  }

  public static HttpEntity<String> getRequestWithNullHeaders() {
    HttpHeaders headers = new HttpHeaders();
    headers.set(Sems1VehicleTestConstants.AUTHORIZATION, null);
    headers.set(Sems1VehicleTestConstants.USERNAME, null);
    headers.set(Sems1VehicleTestConstants.PASSWORD, null);
    return new HttpEntity<>(HTTP_BODY, headers);
  }

  public static String readingJsonRequestFromResource(String resourcePath) throws IOException {
    return IOUtils.toString(
        new FileInputStream(resourcePath), String.valueOf(Charset.defaultCharset()));
  }

  public static HttpEntity<UpdateRequestDTO> getOTAUpdateRequestWithHeaders(String jsonBody)
      throws JsonProcessingException {
    HttpHeaders headers = new HttpHeaders();
    headers.set(
        Sems1VehicleTestConstants.AUTH_HEADER_NAME, Sems1VehicleTestConstants.AUTHORIZATION_VALUE);
    headers.set(Sems1VehicleTestConstants.AUTH_HEADER_TENANT, Sems1VehicleTestConstants.GTT_TENANT);
    headers.setContentType(MediaType.APPLICATION_JSON);
    UpdateRequestDTO updateRequestDTO;
    ObjectMapper mapper = new ObjectMapper();
    updateRequestDTO = mapper.readValue(jsonBody, UpdateRequestDTO.class);
    return new HttpEntity<>(updateRequestDTO, headers);
  }

  public static HttpEntity<UpdateRequestDTO> getOTAUpdateRequestWithHeadersWithNullToken(
      String jsonBody) throws JsonProcessingException {
    HttpHeaders headers = new HttpHeaders();
    headers.set(Sems1VehicleTestConstants.AUTH_HEADER_NAME, null);
    headers.set(Sems1VehicleTestConstants.AUTH_HEADER_TENANT, Sems1VehicleTestConstants.GTT_TENANT);
    headers.setContentType(MediaType.APPLICATION_JSON);
    UpdateRequestDTO updateRequestDTO;
    ObjectMapper mapper = new ObjectMapper();
    updateRequestDTO = mapper.readValue(jsonBody, UpdateRequestDTO.class);
    return new HttpEntity<>(updateRequestDTO, headers);
  }

  public static HttpEntity<UpdateResultDTO> getUpdateResultRequestWithHeaders(String jsonBody)
      throws JsonProcessingException {
    HttpHeaders headers = new HttpHeaders();
    headers.set(
        Sems1VehicleTestConstants.AUTH_HEADER_NAME, Sems1VehicleTestConstants.AUTHORIZATION_VALUE);
    headers.set(
        Sems1VehicleTestConstants.AUTH_HEADER_TENANT,
        Sems1VehicleTestConstants.AUTHORIZATION_VALUE);
    headers.setContentType(MediaType.APPLICATION_JSON);
    UpdateResultDTO updateResultDTO;
    ObjectMapper mapper = new ObjectMapper();
    updateResultDTO = mapper.readValue(jsonBody, UpdateResultDTO.class);
    return new HttpEntity<>(updateResultDTO, headers);
  }

  public static HttpEntity<UpdateResultDTO> getUpdateResultRequestWithHeadersWithNullToken(
      String jsonBody) throws JsonProcessingException {
    HttpHeaders headers = new HttpHeaders();
    headers.set(Sems1VehicleTestConstants.AUTH_HEADER_NAME, null);
    headers.set(
        Sems1VehicleTestConstants.AUTH_HEADER_TENANT,
        Sems1VehicleTestConstants.AUTHORIZATION_VALUE);
    headers.setContentType(MediaType.APPLICATION_JSON);
    UpdateResultDTO updateResultDTO;
    ObjectMapper mapper = new ObjectMapper();
    updateResultDTO = mapper.readValue(jsonBody, UpdateResultDTO.class);
    return new HttpEntity<>(updateResultDTO, headers);
  }

  public static HttpEntity<PentaUpdateRequestDTO> getPentaOTAUpdateRequestWithHeaders(
      String jsonBody) throws JsonProcessingException {
    HttpHeaders headers = new HttpHeaders();
    headers.set(
        Sems1VehicleTestConstants.AUTH_HEADER_NAME, Sems1VehicleTestConstants.AUTHORIZATION_VALUE);
    headers.setContentType(MediaType.APPLICATION_JSON);
    PentaUpdateRequestDTO pentaUpdateRequestDTO;
    ObjectMapper mapper = new ObjectMapper();
    pentaUpdateRequestDTO = mapper.readValue(jsonBody, PentaUpdateRequestDTO.class);
    return new HttpEntity<>(pentaUpdateRequestDTO, headers);
  }

  public static HttpEntity<PentaUpdateRequestDTO> getPentaOTAUpdateRequestWithNullToken(
      String jsonBody) throws JsonProcessingException {
    HttpHeaders headers = new HttpHeaders();
    headers.set(Sems1VehicleTestConstants.AUTH_HEADER_NAME, null);
    headers.setContentType(MediaType.APPLICATION_JSON);
    PentaUpdateRequestDTO pentaUpdateRequestDTO;
    ObjectMapper mapper = new ObjectMapper();
    pentaUpdateRequestDTO = mapper.readValue(jsonBody, PentaUpdateRequestDTO.class);
    return new HttpEntity<>(pentaUpdateRequestDTO, headers);
  }
}
