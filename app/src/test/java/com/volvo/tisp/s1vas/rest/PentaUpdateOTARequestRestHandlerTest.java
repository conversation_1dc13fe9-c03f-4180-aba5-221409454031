package com.volvo.tisp.s1vas.rest;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;

import com.volvo.tisp.s1vas.domain.SecurityGetTokenURLImpl;
import com.volvo.tisp.s1vas.domain.SemsOTARequestManagerImplBean;
import com.volvo.tisp.s1vas.util.Sems1VehicleTestConstants;
import com.volvo.tisp.s1vas.util.TestUtil;
import com.volvo.tisp.sem1core.client.dto.InstallationPackageResponseDTO;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.http.ResponseEntity;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class PentaUpdateOTARequestRestHandlerTest {

  @InjectMocks PentaUpdateOTARequestRestHandler pentaUpdateOTARequestRestHandler;
  @Mock SemsOTARequestManagerImplBean semsOTARequestManagerImplBean;
  @Mock SecurityGetTokenURLImpl securityGetTokenURLImpl;

  PentaUpdateOTARequestRestHandlerTest() throws IOException {}

  String updateRequest =
      TestUtil.readingJsonRequestFromResource(
          Sems1VehicleTestConstants.FILE_ROOT_PATH.concat(
              Sems1VehicleTestConstants.PENTA_UPDATE_OTA_REQUEST));

  @Test
  void testUpdateOTARequestWithValidToken() throws IOException {
    List<InstallationPackageResponseDTO> installationPackageResponseDTOList = new ArrayList<>();
    Mockito.when(semsOTARequestManagerImplBean.validateToken(any()))
        .thenReturn(CompletableFuture.completedFuture(true));
    Mockito.when(semsOTARequestManagerImplBean.handlePentaOTAInputRequestV1(any(), anyLong()))
        .thenReturn(CompletableFuture.completedFuture(installationPackageResponseDTOList));
    CompletionStage<ResponseEntity<List<InstallationPackageResponseDTO>>> response =
        pentaUpdateOTARequestRestHandler.updateOTARequestV1(
            TestUtil.getPentaOTAUpdateRequestWithHeaders(updateRequest));
    assertNotNull(response);
  }

  @Test
  void testUpdateOTARequestWithInvalidToken() throws IOException {
    List<InstallationPackageResponseDTO> installationPackageResponseDTOList = new ArrayList<>();
    Mockito.when(semsOTARequestManagerImplBean.validateToken(any()))
        .thenReturn(CompletableFuture.completedFuture(false));
    Mockito.when(semsOTARequestManagerImplBean.handlePentaOTAInputRequestV1(any(), anyLong()))
        .thenReturn(CompletableFuture.completedFuture(installationPackageResponseDTOList));
    CompletionStage<ResponseEntity<List<InstallationPackageResponseDTO>>> response =
        pentaUpdateOTARequestRestHandler.updateOTARequestV1(
            TestUtil.getPentaOTAUpdateRequestWithHeaders(updateRequest));
    assertNotNull(response);
  }

  @Test
  void testUpdateOTARequestWithNullToken() throws IOException {
    List<InstallationPackageResponseDTO> installationPackageResponseDTOList = new ArrayList<>();
    Mockito.when(securityGetTokenURLImpl.getOTALoginURL(any()))
        .thenReturn(ResponseEntity.of(Optional.of(installationPackageResponseDTOList)));
    CompletionStage<ResponseEntity<List<InstallationPackageResponseDTO>>> response =
        pentaUpdateOTARequestRestHandler.updateOTARequestV1(
            TestUtil.getPentaOTAUpdateRequestWithNullToken(updateRequest));
    assertNotNull(response);
  }
}
