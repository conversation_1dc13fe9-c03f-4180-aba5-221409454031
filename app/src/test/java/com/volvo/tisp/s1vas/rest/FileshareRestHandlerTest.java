package com.volvo.tisp.s1vas.rest;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;

import com.volvo.tisp.s1vas.domain.SecurityGetTokenURLImpl;
import com.volvo.tisp.s1vas.util.FileShareImpl;
import com.volvo.tisp.s1vas.util.Sems1VehicleTestConstants;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.concurrent.*;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.http.ResponseEntity;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class FileshareRestHandlerTest {

  @InjectMocks FileshareRestHandler fileshareRestHandler;
  @Mock FileShareImpl fileShareImpl;
  @Mock SecurityGetTokenURLImpl securityGetTokenURLImpl;
  HttpServletRequest request = mock(HttpServletRequest.class);
  HttpServletResponse response = mock(HttpServletResponse.class);

  @Test
  void testFileShareWithValidHeader()
      throws ExecutionException, InterruptedException, TimeoutException {
    Mockito.when(request.getHeader(Sems1VehicleTestConstants.AUTH_HEADER_NAME))
        .thenReturn(Sems1VehicleTestConstants.AUTH_HEADER_VALUE);
    Mockito.when(request.getParameter(Sems1VehicleTestConstants.AUTH_HEADER_NAME)).thenReturn(null);
    Mockito.when(fileShareImpl.validateToken(any()))
        .thenReturn(CompletableFuture.completedFuture(true));
    Mockito.when(fileShareImpl.validateGetS3(any(), any(), any()))
        .thenReturn(CompletableFuture.completedFuture(null));
    CompletionStage<ResponseEntity<Void>> result =
        fileshareRestHandler.fileShare(Sems1VehicleTestConstants.FILENAME, request, response);
    assert (result
        .toCompletableFuture()
        .get(100, TimeUnit.MILLISECONDS)
        .getStatusCode()
        .is2xxSuccessful());
  }

  @Test
  void testFileShareWithValidParameter()
      throws ExecutionException, InterruptedException, TimeoutException {
    Mockito.when(request.getHeader(Sems1VehicleTestConstants.AUTH_HEADER_NAME)).thenReturn(null);
    Mockito.when(request.getParameter(Sems1VehicleTestConstants.AUTH_HEADER_NAME))
        .thenReturn(Sems1VehicleTestConstants.AUTH_HEADER_VALUE);
    Mockito.when(fileShareImpl.validateToken(any()))
        .thenReturn(CompletableFuture.completedFuture(true));
    Mockito.when(fileShareImpl.validateGetS3(any(), any(), any()))
        .thenReturn(CompletableFuture.completedFuture(null));
    CompletionStage<ResponseEntity<Void>> result =
        fileshareRestHandler.fileShare(Sems1VehicleTestConstants.FILENAME, request, response);
    assert (result
        .toCompletableFuture()
        .get(100, TimeUnit.MILLISECONDS)
        .getStatusCode()
        .is2xxSuccessful());
  }

  @Test
  void testFileShareWithValidHeaderWithInvalidToken() {
    Mockito.when(request.getHeader(Sems1VehicleTestConstants.AUTH_HEADER_NAME))
        .thenReturn(Sems1VehicleTestConstants.AUTH_HEADER_VALUE);
    Mockito.when(request.getParameter(Sems1VehicleTestConstants.AUTH_HEADER_NAME)).thenReturn(null);
    Mockito.when(fileShareImpl.validateToken(any()))
        .thenReturn(CompletableFuture.completedFuture(false));
    Mockito.when(securityGetTokenURLImpl.getFileLoginURL(any())).thenReturn(null);
    CompletionStage<ResponseEntity<Void>> result =
        fileshareRestHandler.fileShare(Sems1VehicleTestConstants.FILENAME, request, response);
    assertNotNull(result);
  }

  @Test
  void testFileShareWithValidParameterWithInvalidToken() {
    Mockito.when(request.getHeader(Sems1VehicleTestConstants.AUTH_HEADER_NAME)).thenReturn(null);
    Mockito.when(request.getParameter(Sems1VehicleTestConstants.AUTH_HEADER_NAME))
        .thenReturn(Sems1VehicleTestConstants.AUTH_HEADER_VALUE);
    Mockito.when(fileShareImpl.validateToken(any()))
        .thenReturn(CompletableFuture.completedFuture(false));
    Mockito.when(securityGetTokenURLImpl.getFileLoginURL(any())).thenReturn(null);
    CompletionStage<ResponseEntity<Void>> result =
        fileshareRestHandler.fileShare(Sems1VehicleTestConstants.FILENAME, request, response);
    assertNotNull(result);
  }

  @Test
  void testFileShareWithValidHeaderWithInvalidTokenAndWithoutFile() {
    Mockito.when(request.getHeader(Sems1VehicleTestConstants.AUTH_HEADER_NAME))
        .thenReturn(Sems1VehicleTestConstants.AUTH_HEADER_VALUE);
    Mockito.when(request.getParameter(Sems1VehicleTestConstants.AUTH_HEADER_NAME)).thenReturn(null);
    Mockito.when(fileShareImpl.validateToken(any()))
        .thenReturn(CompletableFuture.completedFuture(true));
    Mockito.when(securityGetTokenURLImpl.getFileLoginURL(any())).thenReturn(null);
    CompletionStage<ResponseEntity<Void>> result =
        fileshareRestHandler.fileShare(null, request, response);
    assertNotNull(result);
  }

  @Test
  void testFileShareWithValidParameterWithInvalidTokenAndWithoutFile() {
    Mockito.when(request.getHeader(Sems1VehicleTestConstants.AUTH_HEADER_NAME)).thenReturn(null);
    Mockito.when(request.getParameter(Sems1VehicleTestConstants.AUTH_HEADER_NAME))
        .thenReturn(Sems1VehicleTestConstants.AUTH_HEADER_VALUE);
    Mockito.when(fileShareImpl.validateToken(any()))
        .thenReturn(CompletableFuture.completedFuture(true));
    Mockito.when(securityGetTokenURLImpl.getFileLoginURL(any())).thenReturn(null);
    CompletionStage<ResponseEntity<Void>> result =
        fileshareRestHandler.fileShare(null, request, response);
    assertNotNull(result);
  }

  @Test
  void testFileShareWithInvalidHeaderAndParameter() {
    Mockito.when(request.getHeader(Sems1VehicleTestConstants.AUTH_HEADER_NAME)).thenReturn(null);
    Mockito.when(request.getParameter(Sems1VehicleTestConstants.AUTH_HEADER_NAME)).thenReturn(null);
    Mockito.when(securityGetTokenURLImpl.getFileLoginURL(any())).thenReturn(null);
    CompletionStage<ResponseEntity<Void>> result =
        fileshareRestHandler.fileShare(Sems1VehicleTestConstants.FILENAME, request, response);
    assertNotNull(result);
  }
}
