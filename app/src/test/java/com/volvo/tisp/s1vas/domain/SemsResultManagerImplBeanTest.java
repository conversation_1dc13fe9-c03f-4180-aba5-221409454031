package com.volvo.tisp.s1vas.domain;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.volvo.tisp.s1vas.util.Sems1VehicleTestConstants;
import com.volvo.tisp.sem1core.client.OTARequestClient;
import com.volvo.tisp.sem1core.client.dto.*;
import com.volvo.tisp.semsc.client.SemsSecurityClient;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class SemsResultManagerImplBeanTest {

  @InjectMocks SemsResultManagerImplBean semsResultManagerImplBean;
  @Mock OTARequestClient otaRequestClient;
  @Mock SemsSecurityClient semsSecurityClient;

  @Test
  void testHandleOTAResultInputRequest() {
    UpdateResultDTO updateResultDTO = getUpdateResultDTO();
    long startTime = System.currentTimeMillis();
    when(otaRequestClient.updateResultSem1(any()))
        .thenReturn(CompletableFuture.completedFuture(new ArrayList<>()));
    CompletableFuture<List<VehicleInstallationPackageDTO>> response =
        semsResultManagerImplBean.handleOTAResultInputRequest(updateResultDTO, startTime);
    assertNotNull(response);
  }

  @Test
  void testValidateToken() {
    when(semsSecurityClient.isValidToken(any()))
        .thenReturn(CompletableFuture.completedFuture(Sems1VehicleTestConstants.SAMPLE_VALUE));
    CompletableFuture<Boolean> result =
        semsResultManagerImplBean.validateToken(Sems1VehicleTestConstants.AUTHORIZATION_VALUE);
    assertNotNull(result);
  }

  @Test
  void test_ValidateToken() {
    when(semsSecurityClient.isValidToken(any()))
        .thenReturn(CompletableFuture.completedFuture("true"));
    CompletableFuture<Boolean> result =
        semsResultManagerImplBean.validateToken(Sems1VehicleTestConstants.AUTHORIZATION_VALUE);
    assertNotNull(result);
  }

  @Test
  void test_ValidateToken_EmptyString() {
    when(semsSecurityClient.isValidToken(any()))
        .thenReturn(CompletableFuture.completedFuture(Sems1VehicleTestConstants.EMPTY_STRING));
    CompletableFuture<Boolean> result =
        semsResultManagerImplBean.validateToken(Sems1VehicleTestConstants.AUTHORIZATION_VALUE);
    assertNotNull(result);
  }

  private UpdateResultDTO getUpdateResultDTO() {
    UpdateResultDTO updateResultDTO = new UpdateResultDTO();
    updateResultDTO.setPrimaryId(Sems1VehicleTestConstants.PRIMARY_ID);
    updateResultDTO.setSecondaryId(Sems1VehicleTestConstants.SECONDARY_ID);
    List<InstallationPackageResultDTO> listOfInstallationPackages = getListOfInstallationPackages();
    updateResultDTO.setInstallationPackages(listOfInstallationPackages);
    return updateResultDTO;
  }

  private List<InstallationPackageResultDTO> getListOfInstallationPackages() {
    List<InstallationPackageResultDTO> listOfInstallationPackages = new ArrayList<>();
    InstallationPackageResultDTO installationPackageResultDTO = new InstallationPackageResultDTO();
    installationPackageResultDTO.setName(Sems1VehicleTestConstants.PACKAGE_NAME);
    installationPackageResultDTO.setVersionCode(Sems1VehicleTestConstants.VERSION_CODE);
    installationPackageResultDTO.setStatus(Sems1VehicleTestConstants.STATUS);
    listOfInstallationPackages.add(installationPackageResultDTO);
    return listOfInstallationPackages;
  }
}
