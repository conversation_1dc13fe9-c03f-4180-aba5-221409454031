package com.volvo.tisp.s1vas.util;

public class Sems1VehicleTestConstants {

  public static final String PONG = "test";
  public static final String FILESHARE_PING_SUCCESS = "Looking good : ";
  public static final String FILESHARE_PING_FAILURE =
      "Configured fileshare path is not reachable : ";
  public static final String FILESHARE_PATH_VALUE =
      "/var/opt/vgt/sems1-vehicle-api-server-portal/software";
  public static final String EXCEPTION = "Test error";
  public static final String AUTH_HEADER_NAME = "X-AUTH-TOKEN";
  public static final String AUTH_HEADER_VALUE = "validToken";
  public static final String FILENAME = "test.txt";
  public static final String AUTHORIZATION = "Authorization";
  public static final String USERNAME = "Username";
  public static final String PASSWORD = "Password";
  public static final String HTTP_BODY = "body";
  public static final String AUTHORIZATION_VALUE = "075e5abf-9376-4a5a-b53f-05be78bcedc5";
  public static final String USER = "gttblr";
  public static final String SAMPLE_VALUE = "value";
  public static final String FILE_ROOT_PATH = "src/test/resources/";
  public static final String UPDATE_OTA_REQUEST = "OTAUpdateRequest.json";
  public static final String AUTH_HEADER_TENANT = "X-AUTH-TENANT";
  public static final String GTT_TENANT = "gtt";
  public static final String UPDATE_RESULT_REQUEST = "UpdateResultRequest.json";
  public static final String PENTA_UPDATE_OTA_REQUEST = "PentaOTAUpdateRequest.json";
  public static final String PENTA_UPDATERESULT_REQUEST = "PentaUpdateResultRequest.json";
  public static final String FILESHARE_PATH = "fileshare.path";
  public static final String TOKEN_GENERATE_URL = "invalidtoken.url";
  public static final String TOKEN_GENERATE_URL_VALUE =
      "http://localhost:44760/security/v1/api/login";
  public static final String NA_TENANT = "trucks_na";
  public static final String NA_TOKEN_GENERATE_URL = "natoken.url";
  public static final String NA_TOKEN_GENERATE_URL_VALUE = "http://localhost:44760/api/v1/logon";
  public static final String METADATA_PARROT_FIRMWARE_PROPERTY = "parrotFirmwareVersion";
  public static final String METADATA_PARROT_FIRMWARE_PROPERTY_VALUE = "V2.5.65.65_VGTT_EU";
  public static final String METADATA_ANDROID_API_LEVEL_PROPERTY = "androidApiLevel";
  public static final String METADATA_ANDROID_API_LEVEL_PROPERTY_VALUE = "30";
  public static final String PARROT_FIRMWRAE_PACKAGE_NAME = "ParrotFirmware_EU";
  public static final String PARROT_FIRMWARE_VERSION_NAME = "V2.5.65.65_VGTT_EU";
  public static final int PARROT_FIRMWARE_VERSION_CODE = 2056565;
  public static final String TOKEN_NOT_VALID_MESSAGE = "Token is not valid";
  public static final String NOT_FOUND_EXCEPTION_MESSAGE = "The data for Primary id is not found";
  public static final String INTERNAL_ERROR_MESSAGE = "An error occurred while Update OTA";
  public static final String TOO_MANY_REQUESTS_MESSAGE = "Too Many Requests Exception";
  public static final String EMPTY_STRING = "";
  public static final String PRIMARY_ID = "W170E920073";
  public static final String SECONDARY_ID = "00108595";
  public static final String PACKAGE_NAME = "com.volvo.sems.appforautomation2";
  public static final int VERSION_CODE = 1;
  public static final String STATUS = "SUCCESS";
  public static final String REQUEST_THRESHOLD = "Request";
  public static final String DOWNLOAD_THRESHOLD = "Download";
  public static final String COMPONENT_SHORT_NAME = "S1VAS";
  public static final String COMPONENT_LONG_NAME = "sems1-vehicle-api-server";
  public static final String COMPONENT_VERSION = "160";
  public static final String FAILURE_REASON = "Not enough space";
}
