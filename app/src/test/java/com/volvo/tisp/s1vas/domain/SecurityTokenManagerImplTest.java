package com.volvo.tisp.s1vas.domain;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;

import com.volvo.tisp.s1vas.util.Sems1VehicleTestConstants;
import com.volvo.tisp.semsc.client.SemsSecurityClient;
import java.util.concurrent.CompletableFuture;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class SecurityTokenManagerImplTest {

  @InjectMocks SecurityTokenManagerImpl securityTokenManagerImpl;
  @Mock SemsSecurityClient semsSecurityClient;

  @Test
  void testLoginByusernamePass() {
    Mockito.when(semsSecurityClient.getTokenByHeader(any()))
        .thenReturn(CompletableFuture.completedFuture(Sems1VehicleTestConstants.SAMPLE_VALUE));
    CompletableFuture<String> response =
        securityTokenManagerImpl.loginByusernamePass(Sems1VehicleTestConstants.AUTHORIZATION_VALUE);
    assertNotNull(response);
  }

  @Test
  void testLoginByusernamePass_ExceptionDuringTokenGeneration() {
    Mockito.when(semsSecurityClient.getTokenByHeader(any()))
        .thenReturn(
            CompletableFuture.failedFuture(new RuntimeException("Token generation failed")));
    CompletableFuture<String> result =
        securityTokenManagerImpl.loginByusernamePass(Sems1VehicleTestConstants.AUTHORIZATION_VALUE);
    assertNull(result.join());
  }

  @Test
  void testLogin() {
    Mockito.when(semsSecurityClient.getTokenByHeader(any()))
        .thenReturn(CompletableFuture.completedFuture(Sems1VehicleTestConstants.SAMPLE_VALUE));
    CompletableFuture<String> response =
        securityTokenManagerImpl.login(Sems1VehicleTestConstants.AUTHORIZATION_VALUE);
    assertNotNull(response);
  }

  @Test
  void testLogin_ExceptionDuringTokenGeneration() {
    Mockito.when(semsSecurityClient.getTokenByHeader(any()))
        .thenReturn(
            CompletableFuture.failedFuture(new RuntimeException("Token generation failed")));
    CompletableFuture<String> result =
        securityTokenManagerImpl.login(Sems1VehicleTestConstants.AUTHORIZATION_VALUE);
    assertNull(result.join());
  }
}
