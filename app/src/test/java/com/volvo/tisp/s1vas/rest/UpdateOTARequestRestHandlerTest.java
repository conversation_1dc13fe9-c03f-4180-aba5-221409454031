package com.volvo.tisp.s1vas.rest;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.volvo.tisp.s1vas.domain.SecurityGetTokenURLImpl;
import com.volvo.tisp.s1vas.domain.SemsOTARequestManagerImplBean;
import com.volvo.tisp.s1vas.util.Sems1VehicleTestConstants;
import com.volvo.tisp.s1vas.util.TestUtil;
import com.volvo.tisp.sem1core.client.dto.InstallationPackageResponseDTO;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.http.ResponseEntity;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class UpdateOTARequestRestHandlerTest {

  @InjectMocks UpdateOTARequestRestHandler updateOTARequestRestHandler;
  @Mock SemsOTARequestManagerImplBean semsOTARequestManagerImplBean;
  @Mock SecurityGetTokenURLImpl securityGetTokenURLImpl;

  UpdateOTARequestRestHandlerTest() throws IOException {}

  String updateRequest =
      TestUtil.readingJsonRequestFromResource(
          Sems1VehicleTestConstants.FILE_ROOT_PATH.concat(
              Sems1VehicleTestConstants.UPDATE_OTA_REQUEST));

  @Test
  void testUpdateOTARequestWithValidToken() throws JsonProcessingException {
    List<InstallationPackageResponseDTO> installationPackageResponseDTOList = new ArrayList<>();
    Mockito.when(semsOTARequestManagerImplBean.validateToken(any()))
        .thenReturn(CompletableFuture.completedFuture(true));
    Mockito.when(semsOTARequestManagerImplBean.handleOTAInputRequest(any(), anyLong(), any()))
        .thenReturn(CompletableFuture.completedFuture(installationPackageResponseDTOList));
    CompletionStage<ResponseEntity<List<InstallationPackageResponseDTO>>> response =
        updateOTARequestRestHandler.updateOTARequestV(
            TestUtil.getOTAUpdateRequestWithHeaders(updateRequest));
    assertNotNull(response);
  }

  @Test
  void testUpdateOTARequestWithInvalidToken() throws JsonProcessingException {
    List<InstallationPackageResponseDTO> installationPackageResponseDTOList = new ArrayList<>();
    Mockito.when(semsOTARequestManagerImplBean.validateToken(any()))
        .thenReturn(CompletableFuture.completedFuture(false));
    Mockito.when(securityGetTokenURLImpl.getOTALoginURL(any()))
        .thenReturn(ResponseEntity.of(Optional.of(installationPackageResponseDTOList)));
    CompletionStage<ResponseEntity<List<InstallationPackageResponseDTO>>> response =
        updateOTARequestRestHandler.updateOTARequestV(
            TestUtil.getOTAUpdateRequestWithHeaders(updateRequest));
    assertNotNull(response);
  }

  @Test
  void testUpdateOTARequestWithNullToken() throws JsonProcessingException {
    List<InstallationPackageResponseDTO> installationPackageResponseDTOList = new ArrayList<>();
    Mockito.when(securityGetTokenURLImpl.getOTALoginURL(any()))
        .thenReturn(ResponseEntity.of(Optional.of(installationPackageResponseDTOList)));
    CompletionStage<ResponseEntity<List<InstallationPackageResponseDTO>>> response =
        updateOTARequestRestHandler.updateOTARequestV(
            TestUtil.getOTAUpdateRequestWithHeadersWithNullToken(updateRequest));
    assertNotNull(response);
  }

  @Test
  void testUpdateOTARequestV2WithValidToken() throws JsonProcessingException {
    List<InstallationPackageResponseDTO> installationPackageResponseDTOList = new ArrayList<>();
    Mockito.when(semsOTARequestManagerImplBean.validateToken(any()))
        .thenReturn(CompletableFuture.completedFuture(true));
    Mockito.when(semsOTARequestManagerImplBean.handleOTAInputRequestV2(any(), anyLong()))
        .thenReturn(CompletableFuture.completedFuture(installationPackageResponseDTOList));
    CompletionStage<ResponseEntity<List<InstallationPackageResponseDTO>>> response =
        updateOTARequestRestHandler.updateOTARequestV2(
            TestUtil.getOTAUpdateRequestWithHeaders(updateRequest));
    assertNotNull(response);
  }

  @Test
  void testUpdateOTARequestV2WithInvalidToken() throws JsonProcessingException {
    List<InstallationPackageResponseDTO> installationPackageResponseDTOList = new ArrayList<>();
    Mockito.when(semsOTARequestManagerImplBean.validateToken(any()))
        .thenReturn(CompletableFuture.completedFuture(false));
    Mockito.when(securityGetTokenURLImpl.getOTALoginURL(any()))
        .thenReturn(ResponseEntity.of(Optional.of(installationPackageResponseDTOList)));
    CompletionStage<ResponseEntity<List<InstallationPackageResponseDTO>>> response =
        updateOTARequestRestHandler.updateOTARequestV2(
            TestUtil.getOTAUpdateRequestWithHeaders(updateRequest));
    assertNotNull(response);
  }

  @Test
  void testUpdateOTARequestV2WithNullToken() throws JsonProcessingException {
    List<InstallationPackageResponseDTO> installationPackageResponseDTOList = new ArrayList<>();
    Mockito.when(securityGetTokenURLImpl.getOTALoginURL(any()))
        .thenReturn(ResponseEntity.of(Optional.of(installationPackageResponseDTOList)));
    CompletionStage<ResponseEntity<List<InstallationPackageResponseDTO>>> response =
        updateOTARequestRestHandler.updateOTARequestV2(
            TestUtil.getOTAUpdateRequestWithHeadersWithNullToken(updateRequest));
    assertNotNull(response);
  }
}
