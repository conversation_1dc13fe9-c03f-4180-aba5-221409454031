package com.volvo.tisp.s1vas.converter;

import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.volvo.tisp.s1vas.util.Sems1VehicleTestConstants;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.http.ResponseEntity;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class SemsTokenOutputConverterTest {

  @InjectMocks SemsTokenOutputConverter semsTokenOutputConverter;

  @Test
  void testConvertResponse() {
    ResponseEntity<String> response =
        semsTokenOutputConverter.convertResponse(Sems1VehicleTestConstants.AUTHORIZATION_VALUE);
    assertNotNull(response);
  }

  @Test
  void testConvertResponseWithNullToken() {
    ResponseEntity<String> response = semsTokenOutputConverter.convertResponse(null);
    assertNotNull(response);
  }

  @Test
  void testConvert() {
    String response =
        semsTokenOutputConverter.convert(Sems1VehicleTestConstants.AUTHORIZATION_VALUE);
    assertNotNull(response);
  }
}
