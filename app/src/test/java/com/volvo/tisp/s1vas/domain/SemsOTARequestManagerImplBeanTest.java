package com.volvo.tisp.s1vas.domain;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.volvo.tisp.s1vas.util.Sems1VehicleTestConstants;
import com.volvo.tisp.s1vas.util.ThresholdUtil;
import com.volvo.tisp.sem1core.client.PentaOTARequestClient;
import com.volvo.tisp.sem1core.client.dto.*;
import com.volvo.tisp.sem1core.client.types.InstallationPackageStatusType;
import com.volvo.tisp.sem1core.client.types.InstallationPackageType;
import com.volvo.tisp.semsc.client.SemsSecurityClient;
import com.volvo.tisp.semslib.exceptions.*;
import com.wirelesscar.config.Config;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class SemsOTARequestManagerImplBeanTest {

  @InjectMocks SemsOTARequestManagerImplBean semsOTARequestManagerImplBean;
  @Mock SemsSecurityClient semsSecurityClient;
  @Mock ThresholdUtil thresholdUtil;
  @Mock com.volvo.tisp.sem1core.client.OTARequestClient otaSem1Client;
  @Mock Config config;
  @Mock PentaOTARequestClient pentaOTARequestClient;

  @Test
  void test_handleOTAInputRequest_withEnabledRetryAndNonVceOrPentaTenant() {
    UpdateRequestDTO updateRequestDTO = getUpdateRequestDTO();
    long startTime = System.currentTimeMillis();
    when(config.getString(Mockito.anyString())).thenReturn(Optional.of("true"));
    mockCallsForHandleOTAInputRequest();
    when(thresholdUtil.handleOTADataThreshold(
            Mockito.anyString(), Mockito.anyList(), Mockito.anyString()))
        .thenReturn(CompletableFuture.completedFuture(new ArrayList<>()));
    CompletableFuture<List<InstallationPackageResponseDTO>> result =
        semsOTARequestManagerImplBean.handleOTAInputRequest(
            updateRequestDTO, startTime, Sems1VehicleTestConstants.GTT_TENANT);
    assertNotNull(result);
  }

  @Test
  void test_handleOTAInputRequest_with_IllegalArgumentException() {
    UpdateRequestDTO updateRequestDTO = getUpdateRequestDTO();
    long startTime = System.currentTimeMillis();
    when(config.getString(Mockito.anyString())).thenReturn(Optional.of("true"));
    mockCallsForHandleOTAInputRequest();
    when(thresholdUtil.handleResponseList(any())).thenThrow(new IllegalArgumentException());
    CompletableFuture<List<InstallationPackageResponseDTO>> result =
        semsOTARequestManagerImplBean.handleOTAInputRequest(
            updateRequestDTO, startTime, Sems1VehicleTestConstants.GTT_TENANT);
    assertNotNull(result);
  }

  @Test
  void test_handleOTAInputRequest_with_NotFoundException() {
    UpdateRequestDTO updateRequestDTO = getUpdateRequestDTO();
    long startTime = System.currentTimeMillis();
    when(config.getString(Mockito.anyString())).thenReturn(Optional.of("true"));
    mockCallsForHandleOTAInputRequest();
    when(thresholdUtil.handleResponseList(any()))
        .thenThrow(new NotFoundException(Sems1VehicleTestConstants.NOT_FOUND_EXCEPTION_MESSAGE));
    CompletableFuture<List<InstallationPackageResponseDTO>> result =
        semsOTARequestManagerImplBean.handleOTAInputRequest(
            updateRequestDTO, startTime, Sems1VehicleTestConstants.GTT_TENANT);
    assertNotNull(result);
  }

  @Test
  void test_handleOTAInputRequest_with_InvalidCredentialException() {
    UpdateRequestDTO updateRequestDTO = getUpdateRequestDTO();
    long startTime = System.currentTimeMillis();
    when(config.getString(Mockito.anyString())).thenReturn(Optional.of("true"));
    mockCallsForHandleOTAInputRequest();
    when(thresholdUtil.handleResponseList(any()))
        .thenThrow(
            new InvalidCredentialsException(Sems1VehicleTestConstants.TOKEN_NOT_VALID_MESSAGE));
    CompletableFuture<List<InstallationPackageResponseDTO>> result =
        semsOTARequestManagerImplBean.handleOTAInputRequest(
            updateRequestDTO, startTime, Sems1VehicleTestConstants.GTT_TENANT);
    assertNotNull(result);
  }

  @Test
  void test_handleOTAInputRequest_with_ServiceUnavailableException() {
    UpdateRequestDTO updateRequestDTO = getUpdateRequestDTO();
    long startTime = System.currentTimeMillis();
    when(config.getString(Mockito.anyString())).thenReturn(Optional.of("true"));
    mockCallsForHandleOTAInputRequest();
    when(thresholdUtil.handleResponseList(any()))
        .thenThrow(
            new ServiceUnavailableException(Sems1VehicleTestConstants.NOT_FOUND_EXCEPTION_MESSAGE));
    CompletableFuture<List<InstallationPackageResponseDTO>> result =
        semsOTARequestManagerImplBean.handleOTAInputRequest(
            updateRequestDTO, startTime, Sems1VehicleTestConstants.GTT_TENANT);
    assertNotNull(result);
  }

  @Test
  void test_handleOTAInputRequest_with_TooManyRequestsException() {
    UpdateRequestDTO updateRequestDTO = getUpdateRequestDTO();
    long startTime = System.currentTimeMillis();
    when(config.getString(Mockito.anyString())).thenReturn(Optional.of("true"));
    mockCallsForHandleOTAInputRequest();
    when(thresholdUtil.handleResponseList(any()))
        .thenThrow(
            new TooManyRequestsException(Sems1VehicleTestConstants.TOO_MANY_REQUESTS_MESSAGE));
    CompletableFuture<List<InstallationPackageResponseDTO>> result =
        semsOTARequestManagerImplBean.handleOTAInputRequest(
            updateRequestDTO, startTime, Sems1VehicleTestConstants.GTT_TENANT);
    assertNotNull(result);
  }

  @Test
  void test_handleOTAInputRequest_with_InternalServerErrorException() {
    UpdateRequestDTO updateRequestDTO = getUpdateRequestDTO();
    long startTime = System.currentTimeMillis();
    when(config.getString(Mockito.anyString())).thenReturn(Optional.of("true"));
    mockCallsForHandleOTAInputRequest();
    when(thresholdUtil.handleResponseList(any()))
        .thenThrow(
            new InternalServerErrorException(Sems1VehicleTestConstants.INTERNAL_ERROR_MESSAGE));
    CompletableFuture<List<InstallationPackageResponseDTO>> result =
        semsOTARequestManagerImplBean.handleOTAInputRequest(
            updateRequestDTO, startTime, Sems1VehicleTestConstants.GTT_TENANT);
    assertNotNull(result);
  }

  @Test
  void test_handleOTAInputRequestWithVCETenant() {
    UpdateRequestDTO updateRequestDTO = getUpdateRequestDTO();
    long startTime = System.currentTimeMillis();
    when(config.getString(Mockito.anyString())).thenReturn(Optional.of("false"));
    when(otaSem1Client.updateOTASem1(any()))
        .thenReturn(CompletableFuture.completedFuture(new ArrayList<>()));
    CompletableFuture<List<InstallationPackageResponseDTO>> result =
        semsOTARequestManagerImplBean.handleOTAInputRequest(updateRequestDTO, startTime, "vce");
    assertNotNull(result);
  }

  @Test
  void testValidateToken() {
    when(semsSecurityClient.isValidToken(any()))
        .thenReturn(CompletableFuture.completedFuture(Sems1VehicleTestConstants.SAMPLE_VALUE));
    CompletableFuture<Boolean> result =
        semsOTARequestManagerImplBean.validateToken(Sems1VehicleTestConstants.AUTHORIZATION_VALUE);
    assertNotNull(result);
  }

  @Test
  void test_ValidateToken() {
    when(semsSecurityClient.isValidToken(any()))
        .thenReturn(CompletableFuture.completedFuture("true"));
    CompletableFuture<Boolean> result =
        semsOTARequestManagerImplBean.validateToken(Sems1VehicleTestConstants.AUTHORIZATION_VALUE);
    assertNotNull(result);
  }

  @Test
  void test_ValidateToken_EmptyString() {
    when(semsSecurityClient.isValidToken(any()))
        .thenReturn(CompletableFuture.completedFuture(Sems1VehicleTestConstants.EMPTY_STRING));
    CompletableFuture<Boolean> result =
        semsOTARequestManagerImplBean.validateToken(Sems1VehicleTestConstants.AUTHORIZATION_VALUE);
    assertNotNull(result);
  }

  @Test
  void testHandleOTAInputRequestV2() {
    UpdateRequestDTO updateRequestDTO = getUpdateRequestDTO();
    long startTime = System.currentTimeMillis();
    when(otaSem1Client.updateOTASem1(any()))
        .thenReturn(CompletableFuture.completedFuture(new ArrayList<>()));
    CompletableFuture<List<InstallationPackageResponseDTO>> result =
        semsOTARequestManagerImplBean.handleOTAInputRequestV2(updateRequestDTO, startTime);
    assertNotNull(result);
  }

  @Test
  void testHandleOTAInputRequestV2_with_IllegalArgumentException() {
    UpdateRequestDTO updateRequestDTO = getUpdateRequestDTO();
    long startTime = System.currentTimeMillis();
    when(otaSem1Client.updateOTASem1(any()))
        .thenReturn(CompletableFuture.completedFuture(new ArrayList<>()));
    when(thresholdUtil.signAppsFw(any())).thenThrow(new IllegalArgumentException());
    CompletableFuture<List<InstallationPackageResponseDTO>> result =
        semsOTARequestManagerImplBean.handleOTAInputRequestV2(updateRequestDTO, startTime);
    assertNotNull(result);
  }

  @Test
  void testHandleOTAInputRequestV2_with_NotFoundException() {
    UpdateRequestDTO updateRequestDTO = getUpdateRequestDTO();
    long startTime = System.currentTimeMillis();
    when(otaSem1Client.updateOTASem1(any()))
        .thenReturn(CompletableFuture.completedFuture(new ArrayList<>()));
    when(thresholdUtil.signAppsFw(any()))
        .thenThrow(new NotFoundException(Sems1VehicleTestConstants.NOT_FOUND_EXCEPTION_MESSAGE));
    CompletableFuture<List<InstallationPackageResponseDTO>> result =
        semsOTARequestManagerImplBean.handleOTAInputRequestV2(updateRequestDTO, startTime);
    assertNotNull(result);
  }

  @Test
  void testHandleOTAInputRequestV2_with_InvalidCredentialException() {
    UpdateRequestDTO updateRequestDTO = getUpdateRequestDTO();
    long startTime = System.currentTimeMillis();
    when(otaSem1Client.updateOTASem1(any()))
        .thenReturn(CompletableFuture.completedFuture(new ArrayList<>()));
    when(thresholdUtil.signAppsFw(any()))
        .thenThrow(
            new InvalidCredentialsException(Sems1VehicleTestConstants.TOKEN_NOT_VALID_MESSAGE));
    CompletableFuture<List<InstallationPackageResponseDTO>> result =
        semsOTARequestManagerImplBean.handleOTAInputRequestV2(updateRequestDTO, startTime);
    assertNotNull(result);
  }

  @Test
  void testHandleOTAInputRequestV2_with_ServiceUnavailableException() {
    UpdateRequestDTO updateRequestDTO = getUpdateRequestDTO();
    long startTime = System.currentTimeMillis();
    when(otaSem1Client.updateOTASem1(any()))
        .thenReturn(CompletableFuture.completedFuture(new ArrayList<>()));
    when(thresholdUtil.signAppsFw(any()))
        .thenThrow(
            new ServiceUnavailableException(Sems1VehicleTestConstants.NOT_FOUND_EXCEPTION_MESSAGE));
    CompletableFuture<List<InstallationPackageResponseDTO>> result =
        semsOTARequestManagerImplBean.handleOTAInputRequestV2(updateRequestDTO, startTime);
    assertNotNull(result);
  }

  @Test
  void testHandleOTAInputRequestV2_with_TooManyRequestsException() {
    UpdateRequestDTO updateRequestDTO = getUpdateRequestDTO();
    long startTime = System.currentTimeMillis();
    when(otaSem1Client.updateOTASem1(any()))
        .thenReturn(CompletableFuture.completedFuture(new ArrayList<>()));
    when(thresholdUtil.signAppsFw(any()))
        .thenThrow(
            new TooManyRequestsException(Sems1VehicleTestConstants.TOO_MANY_REQUESTS_MESSAGE));
    CompletableFuture<List<InstallationPackageResponseDTO>> result =
        semsOTARequestManagerImplBean.handleOTAInputRequestV2(updateRequestDTO, startTime);
    assertNotNull(result);
  }

  @Test
  void testHandleOTAInputRequestV2_with_InternalServerErrorException() {
    UpdateRequestDTO updateRequestDTO = getUpdateRequestDTO();
    long startTime = System.currentTimeMillis();
    when(otaSem1Client.updateOTASem1(any()))
        .thenReturn(CompletableFuture.completedFuture(new ArrayList<>()));
    when(thresholdUtil.signAppsFw(any()))
        .thenThrow(
            new InternalServerErrorException(Sems1VehicleTestConstants.INTERNAL_ERROR_MESSAGE));
    CompletableFuture<List<InstallationPackageResponseDTO>> result =
        semsOTARequestManagerImplBean.handleOTAInputRequestV2(updateRequestDTO, startTime);
    assertNotNull(result);
  }

  @Test
  void testHandlePentaOTAInputRequestV1() {
    PentaUpdateRequestDTO pentaUpdateRequestDTO = getPentaUpdateRequestDTO();
    long startTime = System.currentTimeMillis();
    when(pentaOTARequestClient.updateOTAForPenta(any()))
        .thenReturn(CompletableFuture.completedFuture(new ArrayList<>()));
    CompletableFuture<List<InstallationPackageResponseDTO>> result =
        semsOTARequestManagerImplBean.handlePentaOTAInputRequestV1(
            pentaUpdateRequestDTO, startTime);
    assertNotNull(result);
  }

  @Test
  void testHandlePentaOTAInputRequestV1_with_IllegalArgumentException() {
    PentaUpdateRequestDTO pentaUpdateRequestDTO = getPentaUpdateRequestDTO();
    long startTime = System.currentTimeMillis();
    when(pentaOTARequestClient.updateOTAForPenta(any()))
        .thenReturn(CompletableFuture.completedFuture(new ArrayList<>()));
    when(thresholdUtil.signAppsFw(any())).thenThrow(new IllegalArgumentException());
    CompletableFuture<List<InstallationPackageResponseDTO>> result =
        semsOTARequestManagerImplBean.handlePentaOTAInputRequestV1(
            pentaUpdateRequestDTO, startTime);
    assertNotNull(result);
  }

  @Test
  void testHandlePentaOTAInputRequestV1_with_NotFoundException() {
    PentaUpdateRequestDTO pentaUpdateRequestDTO = getPentaUpdateRequestDTO();
    long startTime = System.currentTimeMillis();
    when(pentaOTARequestClient.updateOTAForPenta(any()))
        .thenReturn(CompletableFuture.completedFuture(new ArrayList<>()));
    when(thresholdUtil.signAppsFw(any()))
        .thenThrow(new NotFoundException(Sems1VehicleTestConstants.NOT_FOUND_EXCEPTION_MESSAGE));
    CompletableFuture<List<InstallationPackageResponseDTO>> result =
        semsOTARequestManagerImplBean.handlePentaOTAInputRequestV1(
            pentaUpdateRequestDTO, startTime);
    assertNotNull(result);
  }

  @Test
  void testHandlePentaOTAInputRequestV1_with_InvalidCredentialException() {
    PentaUpdateRequestDTO pentaUpdateRequestDTO = getPentaUpdateRequestDTO();
    long startTime = System.currentTimeMillis();
    when(pentaOTARequestClient.updateOTAForPenta(any()))
        .thenReturn(CompletableFuture.completedFuture(new ArrayList<>()));
    when(thresholdUtil.signAppsFw(any()))
        .thenThrow(
            new InvalidCredentialsException(Sems1VehicleTestConstants.TOKEN_NOT_VALID_MESSAGE));
    CompletableFuture<List<InstallationPackageResponseDTO>> result =
        semsOTARequestManagerImplBean.handlePentaOTAInputRequestV1(
            pentaUpdateRequestDTO, startTime);
    assertNotNull(result);
  }

  @Test
  void testHandlePentaOTAInputRequestV1_with_ServiceUnavailableException() {
    PentaUpdateRequestDTO pentaUpdateRequestDTO = getPentaUpdateRequestDTO();
    long startTime = System.currentTimeMillis();
    when(pentaOTARequestClient.updateOTAForPenta(any()))
        .thenReturn(CompletableFuture.completedFuture(new ArrayList<>()));
    when(thresholdUtil.signAppsFw(any()))
        .thenThrow(
            new ServiceUnavailableException(Sems1VehicleTestConstants.NOT_FOUND_EXCEPTION_MESSAGE));
    CompletableFuture<List<InstallationPackageResponseDTO>> result =
        semsOTARequestManagerImplBean.handlePentaOTAInputRequestV1(
            pentaUpdateRequestDTO, startTime);
    assertNotNull(result);
  }

  @Test
  void testHandlePentaOTAInputRequestV1_with_TooManyRequestsException() {
    PentaUpdateRequestDTO pentaUpdateRequestDTO = getPentaUpdateRequestDTO();
    long startTime = System.currentTimeMillis();
    when(pentaOTARequestClient.updateOTAForPenta(any()))
        .thenReturn(CompletableFuture.completedFuture(new ArrayList<>()));
    when(thresholdUtil.signAppsFw(any()))
        .thenThrow(
            new TooManyRequestsException(Sems1VehicleTestConstants.TOO_MANY_REQUESTS_MESSAGE));
    CompletableFuture<List<InstallationPackageResponseDTO>> result =
        semsOTARequestManagerImplBean.handlePentaOTAInputRequestV1(
            pentaUpdateRequestDTO, startTime);
    assertNotNull(result);
  }

  @Test
  void testHandlePentaOTAInputRequestV1_with_InternalServerErrorException() {
    PentaUpdateRequestDTO pentaUpdateRequestDTO = getPentaUpdateRequestDTO();
    long startTime = System.currentTimeMillis();
    when(pentaOTARequestClient.updateOTAForPenta(any()))
        .thenReturn(CompletableFuture.completedFuture(new ArrayList<>()));
    when(thresholdUtil.signAppsFw(any()))
        .thenThrow(
            new InternalServerErrorException(Sems1VehicleTestConstants.INTERNAL_ERROR_MESSAGE));
    CompletableFuture<List<InstallationPackageResponseDTO>> result =
        semsOTARequestManagerImplBean.handlePentaOTAInputRequestV1(
            pentaUpdateRequestDTO, startTime);
    assertNotNull(result);
  }

  private void mockCallsForHandleOTAInputRequest() {
    when(thresholdUtil.handleOTARequestThreshold(
            Mockito.anyString(), Mockito.anyString(), Mockito.anyString()))
        .thenReturn(CompletableFuture.completedFuture(null));
    when(otaSem1Client.updateOTASem1(any()))
        .thenReturn(CompletableFuture.completedFuture(new ArrayList<>()));
    when(thresholdUtil.handleResponseList(Mockito.anyList())).thenReturn(new ArrayList<>());
  }

  private UpdateRequestDTO getUpdateRequestDTO() {
    UpdateRequestDTO updateRequestDTO = new UpdateRequestDTO();
    updateRequestDTO.setPrimaryId(Sems1VehicleTestConstants.PRIMARY_ID);
    updateRequestDTO.setSecondaryId(Sems1VehicleTestConstants.SECONDARY_ID);
    List<MetadataRequestDTO> metadataRequestDTOList = getListOfMetadata();
    updateRequestDTO.setMetadata(metadataRequestDTOList);
    List<InstallationPackageRequestDTO> listOfInstallationPackages =
        getListOfInstallationPackages();
    updateRequestDTO.setInstallationPackages(listOfInstallationPackages);
    return updateRequestDTO;
  }

  private PentaUpdateRequestDTO getPentaUpdateRequestDTO() {
    PentaUpdateRequestDTO pentaUpdateRequestDTO = new PentaUpdateRequestDTO();
    pentaUpdateRequestDTO.setPrimaryId(Sems1VehicleTestConstants.PRIMARY_ID);
    pentaUpdateRequestDTO.setSecondaryId(Sems1VehicleTestConstants.SECONDARY_ID);
    List<MetadataRequestDTO> metadataRequestDTOList = getListOfMetadata();
    pentaUpdateRequestDTO.setMetadata(metadataRequestDTOList);
    List<InstallationPackageRequestDTO> listOfInstallationPackages =
        getListOfInstallationPackages();
    pentaUpdateRequestDTO.setInstallationPackages(listOfInstallationPackages);
    return pentaUpdateRequestDTO;
  }

  private List<InstallationPackageRequestDTO> getListOfInstallationPackages() {
    List<InstallationPackageRequestDTO> listOfInstallationPackages = new ArrayList<>();
    InstallationPackageRequestDTO parrotFirmwareInstallationPackage =
        new InstallationPackageRequestDTO();
    parrotFirmwareInstallationPackage.setName(
        Sems1VehicleTestConstants.PARROT_FIRMWRAE_PACKAGE_NAME);
    parrotFirmwareInstallationPackage.setVersionName(
        Sems1VehicleTestConstants.PARROT_FIRMWARE_VERSION_NAME);
    parrotFirmwareInstallationPackage.setVersionCode(
        Sems1VehicleTestConstants.PARROT_FIRMWARE_VERSION_CODE);
    parrotFirmwareInstallationPackage.setType(InstallationPackageType.FIRMWARE);
    parrotFirmwareInstallationPackage.setStatus(InstallationPackageStatusType.ENABLED);
    listOfInstallationPackages.add(parrotFirmwareInstallationPackage);
    return listOfInstallationPackages;
  }

  private List<MetadataRequestDTO> getListOfMetadata() {
    List<MetadataRequestDTO> metadataRequestDTOList = new ArrayList<>();
    MetadataRequestDTO parrotFirmwareVersionMetadata = new MetadataRequestDTO();
    parrotFirmwareVersionMetadata.setKey(
        Sems1VehicleTestConstants.METADATA_PARROT_FIRMWARE_PROPERTY);
    parrotFirmwareVersionMetadata.setValue(
        Sems1VehicleTestConstants.METADATA_PARROT_FIRMWARE_PROPERTY_VALUE);
    MetadataRequestDTO androidApiLevelMetadata = new MetadataRequestDTO();
    androidApiLevelMetadata.setKey(Sems1VehicleTestConstants.METADATA_ANDROID_API_LEVEL_PROPERTY);
    androidApiLevelMetadata.setValue(
        Sems1VehicleTestConstants.METADATA_ANDROID_API_LEVEL_PROPERTY_VALUE);
    metadataRequestDTOList.add(parrotFirmwareVersionMetadata);
    metadataRequestDTOList.add(androidApiLevelMetadata);
    return metadataRequestDTOList;
  }
}
