package com.volvo.tisp.s1vas.domain;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.*;

import com.volvo.tisp.s1vas.util.Sems1VehicleTestConstants;
import com.wirelesscar.config.Config;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class DeepPingManagerImplTest {

  @InjectMocks DeepPingManagerImpl deepPingManagerImpl;
  @Mock Config config;

  @Test
  void testFileShareDeepPingWithExistentPath() {
    when(config.getString(Sems1VehicleTestConstants.FILESHARE_PATH))
        .thenReturn(
            Optional.of(
                Sems1VehicleTestConstants.FILE_ROOT_PATH
                    + Sems1VehicleTestConstants.PENTA_UPDATE_OTA_REQUEST));
    String response = deepPingManagerImpl.fileShareDeepPing();
    assertNotNull(response);
  }

  @Test
  void testFileShareDeepPingWithNonExistentPath() {
    when(config.getString(Sems1VehicleTestConstants.FILESHARE_PATH))
        .thenReturn(Optional.of(Sems1VehicleTestConstants.FILESHARE_PATH_VALUE));
    String response = deepPingManagerImpl.fileShareDeepPing();
    assertNotNull(response);
  }
}
