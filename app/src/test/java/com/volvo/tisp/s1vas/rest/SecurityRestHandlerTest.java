package com.volvo.tisp.s1vas.rest;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;

import com.volvo.tisp.s1vas.converter.SemsTokenOutputConverter;
import com.volvo.tisp.s1vas.domain.SecurityTokenManagerImpl;
import com.volvo.tisp.s1vas.util.Sems1VehicleTestConstants;
import com.volvo.tisp.s1vas.util.TestUtil;
import com.volvo.tisp.semslib.exceptions.InvalidCredentialsException;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.http.HttpEntity;
import org.springframework.http.ResponseEntity;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class SecurityRestHandlerTest {

  @InjectMocks SecurityRestHandler securityRestHandler;
  @Mock SecurityTokenManagerImpl securityTokenManagerImpl;
  @Mock SemsTokenOutputConverter semsTokenOutputConverter;

  @Test
  void testGetToken() {
    Mockito.when(securityTokenManagerImpl.login(any()))
        .thenReturn(
            CompletableFuture.completedFuture(Sems1VehicleTestConstants.AUTHORIZATION_VALUE));
    Mockito.when(semsTokenOutputConverter.convertResponse(any()))
        .thenReturn(ResponseEntity.of(Optional.of(Sems1VehicleTestConstants.AUTH_HEADER_VALUE)));
    CompletionStage<ResponseEntity<String>> response =
        securityRestHandler.getToken(TestUtil.getRequestWithHeader());
    assertNotNull(response);
  }

  @Test
  void testGetTokenWithNullAuthorizationValue() {
    Mockito.when(securityTokenManagerImpl.loginByusernamePass(any()))
        .thenReturn(
            CompletableFuture.completedFuture(Sems1VehicleTestConstants.AUTHORIZATION_VALUE));
    Mockito.when(semsTokenOutputConverter.convertResponse(any()))
        .thenReturn(ResponseEntity.of(Optional.of(Sems1VehicleTestConstants.AUTH_HEADER_VALUE)));
    CompletionStage<ResponseEntity<String>> response =
        securityRestHandler.getToken(TestUtil.getRequestWithNullAuthorizationValue());
    assertNotNull(response);
  }

  @Test
  void testGetTokenWithNullHeaders() {
    CompletionStage<ResponseEntity<String>> response =
        securityRestHandler.getToken(TestUtil.getRequestWithNullHeaders());
    assertNotNull(response);
  }

  @Test
  void testPostToken() {
    Mockito.when(securityTokenManagerImpl.login(any()))
        .thenReturn(
            CompletableFuture.completedFuture(Sems1VehicleTestConstants.AUTHORIZATION_VALUE));
    Mockito.when(semsTokenOutputConverter.convertResponse(any()))
        .thenReturn(ResponseEntity.of(Optional.of(Sems1VehicleTestConstants.AUTH_HEADER_VALUE)));
    CompletionStage<ResponseEntity<String>> response =
        securityRestHandler.postToken(TestUtil.getRequestWithHeader());
    assertNotNull(response);
  }

  @Test
  void testPostTokenWithNullAuthorizationValue() {
    Mockito.when(securityTokenManagerImpl.loginByusernamePass(any()))
        .thenReturn(
            CompletableFuture.completedFuture(Sems1VehicleTestConstants.AUTHORIZATION_VALUE));
    Mockito.when(semsTokenOutputConverter.convertResponse(any()))
        .thenReturn(ResponseEntity.of(Optional.of(Sems1VehicleTestConstants.AUTH_HEADER_VALUE)));
    CompletionStage<ResponseEntity<String>> response =
        securityRestHandler.postToken(TestUtil.getRequestWithNullAuthorizationValue());
    assertNotNull(response);
  }

  @Test
  void test_postToken_withNullCredentials() {
    HttpEntity<String> request = TestUtil.getRequestWithNullHeaders();
    assertThrows(InvalidCredentialsException.class, () -> securityRestHandler.postToken(request));
  }
}
