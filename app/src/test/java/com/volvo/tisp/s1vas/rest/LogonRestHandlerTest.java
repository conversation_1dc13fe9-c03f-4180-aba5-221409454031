package com.volvo.tisp.s1vas.rest;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;

import com.volvo.tisp.s1vas.converter.SemsTokenOutputConverter;
import com.volvo.tisp.s1vas.util.Sems1VehicleTestConstants;
import com.volvo.tisp.s1vas.util.TestUtil;
import com.volvo.tisp.sem1core.client.LogonClient;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.concurrent.ExecutionException;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.http.ResponseEntity;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class LogonRestHandlerTest {

  @InjectMocks LogonRestHandler logonRestHandler;
  @Mock SemsTokenOutputConverter semsTokenOutputConverter;
  @Mock LogonClient logonSem1Client;

  @Test
  void testLogOn() throws ExecutionException, InterruptedException {
    Mockito.when(logonSem1Client.logon(any()))
        .thenReturn(CompletableFuture.completedFuture(Sems1VehicleTestConstants.SAMPLE_VALUE));
    Mockito.when(semsTokenOutputConverter.convert(any())).thenReturn(null);
    CompletionStage<ResponseEntity<String>> response =
        logonRestHandler.logon(TestUtil.getRequestWithHeader());
    assertNotNull(response);
  }

  @Test
  void testLogOnWithAuthorizationValueAsNull() throws ExecutionException, InterruptedException {
    Mockito.when(logonSem1Client.logon(any()))
        .thenReturn(CompletableFuture.completedFuture(Sems1VehicleTestConstants.SAMPLE_VALUE));
    Mockito.when(semsTokenOutputConverter.convert(any())).thenReturn(null);
    CompletionStage<ResponseEntity<String>> response =
        logonRestHandler.logon(TestUtil.getRequestWithNullAuthorizationValue());
    assertNotNull(response);
  }

  @Test
  void testLogOnWithAuthorizationValueAndPasswordAsNull()
      throws ExecutionException, InterruptedException {
    Mockito.when(logonSem1Client.logon(any()))
        .thenReturn(CompletableFuture.completedFuture(Sems1VehicleTestConstants.SAMPLE_VALUE));
    Mockito.when(semsTokenOutputConverter.convert(any())).thenReturn(null);
    CompletionStage<ResponseEntity<String>> response =
        logonRestHandler.logon(TestUtil.getRequestWithoutPasswordInHeaders());
    assertNull(response);
  }
}
