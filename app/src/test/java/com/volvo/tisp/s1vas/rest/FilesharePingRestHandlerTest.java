package com.volvo.tisp.s1vas.rest;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;

import com.volvo.check.v1.CheckResponse;
import com.volvo.tisp.s1vas.converter.CheckResponseOutputConverter;
import com.volvo.tisp.s1vas.domain.DeepPingManagerImpl;
import com.volvo.tisp.s1vas.util.Sems1VehicleTestConstants;
import java.util.concurrent.CompletionStage;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class FilesharePingRestHandlerTest {

  @InjectMocks FilesharePingRestHandler filesharePingRestHandler;
  @Mock DeepPingManagerImpl deepPingManagerImpl;
  @Mock CheckResponseOutputConverter checkResponseOutputConverter;

  @Test
  void testShallowPingReturnsResponseEntityWithPongAndOkStatus() {
    ResponseEntity response = filesharePingRestHandler.shallowPing(Sems1VehicleTestConstants.PONG);
    assertEquals(HttpStatus.OK, response.getStatusCode());
    assertEquals(Sems1VehicleTestConstants.PONG, response.getBody());
  }

  @Test
  void testPingFileShareWithPingSuccess() {
    CheckResponse checkResponse = new CheckResponse();
    String pingStatus =
        Sems1VehicleTestConstants.FILESHARE_PING_SUCCESS
            + Sems1VehicleTestConstants.FILESHARE_PATH_VALUE;
    Mockito.when(deepPingManagerImpl.fileShareDeepPing()).thenReturn(pingStatus);
    Mockito.when(checkResponseOutputConverter.convert(any())).thenReturn(checkResponse);
    CompletionStage<CheckResponse> response = filesharePingRestHandler.pingFileshare();
    assertNotNull(response);
  }

  @Test
  void testPingFileShareWithPingFailure() {
    CheckResponse checkResponse = new CheckResponse();
    String pingStatus =
        Sems1VehicleTestConstants.FILESHARE_PING_FAILURE
            + Sems1VehicleTestConstants.FILESHARE_PATH_VALUE;
    Mockito.when(deepPingManagerImpl.fileShareDeepPing()).thenReturn(pingStatus);
    Mockito.when(checkResponseOutputConverter.convert(any())).thenReturn(checkResponse);
    CompletionStage<CheckResponse> response = filesharePingRestHandler.pingFileshare();
    assertNotNull(response);
  }

  @Test
  void testPingFileShareWithException() {
    Mockito.when(deepPingManagerImpl.fileShareDeepPing())
        .thenThrow(new RuntimeException(Sems1VehicleTestConstants.EXCEPTION));
    Mockito.when(checkResponseOutputConverter.convert(any())).thenReturn(new CheckResponse());
    CompletionStage<CheckResponse> result = filesharePingRestHandler.pingFileshare();
    CheckResponse response = result.toCompletableFuture().join();
    assertNotNull(response);
  }
}
