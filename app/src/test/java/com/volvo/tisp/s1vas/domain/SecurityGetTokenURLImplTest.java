package com.volvo.tisp.s1vas.domain;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.when;

import com.volvo.tisp.s1vas.util.Sems1VehicleTestConstants;
import com.volvo.tisp.sem1core.client.dto.InstallationPackageResponseDTO;
import com.volvo.tisp.sem1core.client.dto.VehicleInstallationPackageDTO;
import com.wirelesscar.config.Config;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.http.ResponseEntity;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class SecurityGetTokenURLImplTest {

  @InjectMocks SecurityGetTokenURLImpl securityGetTokenURLImpl;
  @Mock Config config;

  @Test
  void testGetResultLoginURL() {
    when(config.getString(Sems1VehicleTestConstants.TOKEN_GENERATE_URL))
        .thenReturn(Optional.of(Sems1VehicleTestConstants.TOKEN_GENERATE_URL_VALUE));
    ResponseEntity<List<VehicleInstallationPackageDTO>> response =
        securityGetTokenURLImpl.getResultLoginURL(Sems1VehicleTestConstants.GTT_TENANT);
    assertNotNull(response);
  }

  @Test
  void testGetResultLoginURLForNATenant() {
    when(config.getString(Sems1VehicleTestConstants.NA_TOKEN_GENERATE_URL))
        .thenReturn(Optional.of(Sems1VehicleTestConstants.NA_TOKEN_GENERATE_URL_VALUE));
    ResponseEntity<List<VehicleInstallationPackageDTO>> response =
        securityGetTokenURLImpl.getResultLoginURL(Sems1VehicleTestConstants.NA_TENANT);
    assertNotNull(response);
  }

  @Test
  void testOTALoginURL() {
    when(config.getString(Sems1VehicleTestConstants.TOKEN_GENERATE_URL))
        .thenReturn(Optional.of(Sems1VehicleTestConstants.TOKEN_GENERATE_URL_VALUE));
    ResponseEntity<List<InstallationPackageResponseDTO>> response =
        securityGetTokenURLImpl.getOTALoginURL(Sems1VehicleTestConstants.GTT_TENANT);
    assertNotNull(response);
  }

  @Test
  void testOTALoginURLForNATenant() {
    when(config.getString(Sems1VehicleTestConstants.NA_TOKEN_GENERATE_URL))
        .thenReturn(Optional.of(Sems1VehicleTestConstants.NA_TOKEN_GENERATE_URL_VALUE));
    ResponseEntity<List<InstallationPackageResponseDTO>> response =
        securityGetTokenURLImpl.getOTALoginURL(Sems1VehicleTestConstants.NA_TENANT);
    assertNotNull(response);
  }

  @Test
  void testGetFileLoginURL() {
    when(config.getString(Sems1VehicleTestConstants.TOKEN_GENERATE_URL))
        .thenReturn(Optional.of(Sems1VehicleTestConstants.TOKEN_GENERATE_URL_VALUE));
    ResponseEntity<Void> response =
        securityGetTokenURLImpl.getFileLoginURL(Sems1VehicleTestConstants.AUTHORIZATION_VALUE);
    assertNotNull(response);
  }
}
