<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

  <modelVersion>4.0.0</modelVersion>

  <!-- +=============================================== -->

  <!-- | Section 1: Project information -->

  <!-- +=============================================== -->

  <parent>
    <groupId>com.volvo.tisp</groupId>
    <artifactId>tisp-parent</artifactId>
    <version>157</version>
    <relativePath/>
  </parent>

  <groupId>com.volvo.tisp.s1vas</groupId>
  <artifactId>sems1-vehicle-api-server</artifactId>
  <version>0-SNAPSHOT</version>
  <packaging>pom</packaging>

  <name>Sems1 Vehicle Api :: Server</name>
  <description>Description of component</description>
  <url/>

  <properties>
    <tisp-dependencies.version>157</tisp-dependencies.version>
    <sems1-vehicle-api-client.version>12</sems1-vehicle-api-client.version>
    <sems1-core-client.version>97</sems1-core-client.version>
    <jersey-client.version>2.27</jersey-client.version>
    <org.json.doc.version>1.2.5</org.json.doc.version>
    <commons-lang3.version>3.4</commons-lang3.version>
    <component.long-name>sems1-vehicle-api-server</component.long-name>
    <component.short-name>s1vas</component.short-name>
    <component.debug.port>8787</component.debug.port>
    <component.debug.suspend>n</component.debug.suspend>
    <sems-security-client.version>63</sems-security-client.version>
    <aws-java-sdk.version>1.12.713</aws-java-sdk.version>
    <dss.doc.version>5.5.d4j.1</dss.doc.version>
    <bouncycastle.version>1.60</bouncycastle.version>
    <sems-usage-client.version>37</sems-usage-client.version>
    <functional-checks-api.version>0</functional-checks-api.version>
    <sems-lib.version>30</sems-lib.version>
    <maven.compiler.release>17</maven.compiler.release>
    <courier.client.version>98</courier.client.version>
    <commons-io.version>2.15.1</commons-io.version>
    <!--line-coverage update it to 0.80 and missed-class-count to 10 once coverage is in place-->
    <code-quality.minimum-bundle-line-coverage>0.00</code-quality.minimum-bundle-line-coverage>
    <code-quality.minimum-package-line-coverage>0.00</code-quality.minimum-package-line-coverage>
    <code-quality.maximum-missed-class-count>35</code-quality.maximum-missed-class-count>
    <jacoco-maven-plugin.version>0.8.12</jacoco-maven-plugin.version>
  </properties>

  <!-- +=============================================== -->
  <!-- | Section 3: Dependency (Management) -->
  <!-- +=============================================== -->
  <dependencyManagement>
    <dependencies>
      <!-- Our API -->
      <dependency>
        <groupId>com.volvo.tisp.s1vac</groupId>
        <artifactId>sems1-vehicle-api-client-api</artifactId>
        <version>${sems1-vehicle-api-client.version}</version>
      </dependency>
      <dependency>
        <groupId>com.volvo.tisp.s1vac</groupId>
        <artifactId>sems1-vehicle-api-client-impl-http</artifactId>
        <version>${sems1-vehicle-api-client.version}</version>
      </dependency>
      <!-- SEMS1-CORE-CLIENT API -->
      <dependency>
        <groupId>com.volvo.tisp.sem1core</groupId>
        <artifactId>sems1-core-client-api</artifactId>
        <version>${sems1-core-client.version}</version>
      </dependency>
      <dependency>
        <groupId>com.volvo.tisp.sem1core</groupId>
        <artifactId>sems1-core-client-impl-http</artifactId>
        <version>${sems1-core-client.version}</version>
      </dependency>
      <dependency>
        <groupId>com.volvo.tisp</groupId>
        <artifactId>tisp-dependencies</artifactId>
        <version>${tisp-dependencies.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>

      <!-- SEMS-SECURITY API -->
      <dependency>
        <groupId>com.volvo.tisp.semsc</groupId>
        <artifactId>sems-security-client-api</artifactId>
        <version>${sems-security-client.version}</version>
      </dependency>
      <dependency>
        <groupId>com.volvo.tisp.semsc</groupId>
        <artifactId>sems-security-client-impl-http</artifactId>
        <version>${sems-security-client.version}</version>
      </dependency>
      <!--sems usage client component -->
      <dependency>
        <groupId>com.volvo.tisp.suc</groupId>
        <artifactId>sems-usage-client-api</artifactId>
        <version>${sems-usage-client.version}</version>
      </dependency>
      <dependency>
        <groupId>com.volvo.tisp.suc</groupId>
        <artifactId>sems-usage-client-impl-http</artifactId>
        <version>${sems-usage-client.version}</version>
      </dependency>

      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk</artifactId>
        <version>{aws-java-sdk.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-lang3</artifactId>
        <version>${commons-lang3.version}</version>
      </dependency>
      <dependency>
        <groupId>com.wirelesscar.functionalchecks</groupId>
        <artifactId>functional-checks-api</artifactId>
        <version>${functional-checks-api.version}</version>
      </dependency>
      <dependency>
        <groupId>commons-io</groupId>
        <artifactId>commons-io</artifactId>
        <version>${commons-io.version}</version>
      </dependency>
      <!-- sems -lib -->
      <dependency>
        <groupId>com.volvo.tisp.semslib</groupId>
        <artifactId>sems-lib-sems-utils</artifactId>
        <version>${sems-lib.version}</version>
      </dependency>
      <!-- VSCS -->

      <dependency>
        <groupId>com.volvo.tisp.courier</groupId>
        <artifactId>courier-service-api</artifactId>
        <version>${courier.client.version}</version>
      </dependency>
      <dependency>
        <groupId>com.volvo.tisp.courier</groupId>
        <artifactId>courier-service-http-client</artifactId>
        <version>${courier.client.version}</version>
      </dependency>
      <dependency>
        <groupId>com.volvo.tisp.courier</groupId>
        <artifactId>courier-service-jms-client</artifactId>
        <version>${courier.client.version}</version>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <build>
    <plugins>
      <plugin>
        <artifactId>maven-site-plugin</artifactId>
        <executions>
          <execution>
            <id>attach-descriptor</id>
            <goals>
              <goal>attach-descriptor</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.jacoco</groupId>
        <artifactId>jacoco-maven-plugin</artifactId>
        <version>${jacoco-maven-plugin.version}</version>
        <executions>
          <execution>
            <goals>
              <goal>prepare-agent</goal>
            </goals>
          </execution>
          <!-- attached to Maven test phase -->
          <execution>
            <id>report</id>
            <goals>
              <goal>report</goal>
            </goals>
            <phase>test</phase>
          </execution>
          <execution>
            <id>jacoco-check</id>
            <goals>
              <goal>check</goal>
            </goals>
            <phase>test</phase>
            <configuration>
              <!--<skip>${skipTests}</skip>-->
              <rules>
                <rule>
                  <element>BUNDLE</element>
                  <limits>
                    <limit>
                      <counter>LINE</counter>
                      <value>COVEREDRATIO</value>
                      <minimum>${code-quality.minimum-bundle-line-coverage}</minimum>
                    </limit>
                    <limit>
                      <counter>CLASS</counter>
                      <value>MISSEDCOUNT</value>
                      <maximum>${code-quality.maximum-missed-class-count}</maximum>
                    </limit>
                  </limits>
                </rule>
                <rule>
                  <element>PACKAGE</element>
                  <limits>
                    <limit>
                      <counter>LINE</counter>
                      <value>COVEREDRATIO</value>
                      <minimum>${code-quality.minimum-package-line-coverage}</minimum>
                    </limit>
                  </limits>
                </rule>
              </rules>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>

  </build>
  <reporting>
    <plugins>
      <plugin>
        <artifactId>maven-project-info-reports-plugin</artifactId>
        <configuration>
          <dependencyDetailsEnabled>false</dependencyDetailsEnabled>
          <!-- Enhance performance -->
          <dependencyLocationsEnabled>false</dependencyLocationsEnabled>
          <!-- Enhance performance -->
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-surefire-report-plugin</artifactId>
        <configuration>
          <aggregate>true</aggregate>
          <!-- Put all test reports in root site, needed for Overseer -->
          <linkXRef>false</linkXRef>
          <!-- Get rid of warning -->
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>findbugs-maven-plugin</artifactId>
      </plugin>
    </plugins>
  </reporting>

  <!-- +=============================================== -->
  <!-- | Section 2: Module definitions -->
  <!-- +=============================================== -->

  <profiles>
    <profile>
      <id>s1vas-default</id>
      <activation>
        <activeByDefault>true</activeByDefault>
      </activation>
      <modules>
        <module>app</module>
      </modules>
    </profile>
    <profile>
      <id>deployable-assembly</id>
      <activation>
        <property>
          <name>deployable-assembly</name>
        </property>
      </activation>
      <modules>
        <module>deployable-assembly</module>
      </modules>
    </profile>
    <profile>
      <id>vagrant</id>
      <modules>
        <module>app</module>
        <module>deployable-assembly</module>
      </modules>
      <properties>
        <env>vagrant</env>
      </properties>
      <build>
        <defaultGoal>clean pre-integration-test</defaultGoal>
      </build>
    </profile>
    <profile>
      <id>component-tests</id>
      <modules>
        <module>component-tests</module>
      </modules>
    </profile>
    <profile>
      <id>load-tests</id>
      <modules>
        <module>load-tests</module>
      </modules>
    </profile>
  </profiles>

</project>
