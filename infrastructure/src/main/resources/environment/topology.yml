# // TODO // DOJO-OPTIONAL-AT-STEP-1 // # *move* this file to a new repository "yourcomponent-server-infrastructure" into a directory
# // TODO // DOJO-OPTIONAL-AT-STEP-1 // # infrastructure/src/main/resources/ in there, and make sure you delete it from the -server repo
# // TODO // DOJO-OPTIONAL-AT-STEP-1 // # (and then remove this entire comment block)
stacks:
    - microService:
       endpoints:
          loadbalancer:
            port: 44760
            protocol: HTTP
          instances:
            port: 44760
            protocol: HTTP
            ping: /ping/pong
            directAccess:
              http-in-a:
                protocol: tcp
                cidr: ?private-software-comp-a
                port: 44760
              http-in-b:
                protocol: tcp
                cidr: ?private-software-comp-b
                port: 44760

       subnets:
          loadbalancers:
            - subnet: ?private-software-elb-comp-a
            - subnet: ?private-software-elb-comp-b
          instances:
            - subnet: ?private-software-comp-a
            - subnet: ?private-software-comp-b
       scale:
          instances:
            min: 1
            max: 2
            autoScale: CPU
            startupTime: 600
